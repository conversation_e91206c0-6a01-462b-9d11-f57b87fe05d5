using Microsoft.EntityFrameworkCore;
using OfficersManagement.Models;

namespace OfficersManagement.Data
{
    public static class SeedData
    {
        public static async Task SeedProvincesAsync(ApplicationDbContext context)
        {
            // التحقق من وجود محافظات
            if (await context.Provinces.AnyAsync())
            {
                return; // البيانات موجودة مسبقاً
            }

            var provinces = new List<Province>
            {
                new Province { Name = "بغداد", Description = "العاصمة العراقية" },
                new Province { Name = "البصرة", Description = "محافظة البصرة الجنوبية" },
                new Province { Name = "نينوى", Description = "محافظة نينوى الشمالية" },
                new Province { Name = "أربيل", Description = "عاصمة إقليم كردستان" },
                new Province { Name = "النجف", Description = "المحافظة المقدسة" },
                new Province { Name = "كربلاء", Description = "المحافظة المقدسة" },
                new Province { Name = "الأنبار", Description = "أكبر المحافظات العراقية" },
                new Province { Name = "دهوك", Description = "محافظة دهوك الكردية" },
                new Province { Name = "السليمانية", Description = "محافظة السليمانية الكردية" },
                new Province { Name = "كركوك", Description = "محافظة كركوك النفطية" },
                new Province { Name = "ديالى", Description = "محافظة ديالى الشرقية" },
                new Province { Name = "صلاح الدين", Description = "محافظة صلاح الدين الوسطى" },
                new Province { Name = "بابل", Description = "محافظة بابل التاريخية" },
                new Province { Name = "واسط", Description = "محافظة واسط الوسطى" },
                new Province { Name = "ذي قار", Description = "محافظة ذي قار الجنوبية" },
                new Province { Name = "المثنى", Description = "محافظة المثنى الجنوبية" },
                new Province { Name = "القادسية", Description = "محافظة القادسية الوسطى" },
                new Province { Name = "ميسان", Description = "محافظة ميسان الشرقية" },
                new Province { Name = "حلبجة", Description = "محافظة حلبجة الكردية" }
            };

            context.Provinces.AddRange(provinces);
            await context.SaveChangesAsync();
        }

        public static async Task SeedDistrictsAsync(ApplicationDbContext context)
        {
            // التحقق من وجود أقضية
            if (await context.Districts.AnyAsync())
            {
                return; // البيانات موجودة مسبقاً
            }

            // الحصول على المحافظات
            var baghdad = await context.Provinces.FirstOrDefaultAsync(p => p.Name == "بغداد");
            var basra = await context.Provinces.FirstOrDefaultAsync(p => p.Name == "البصرة");
            var ninawa = await context.Provinces.FirstOrDefaultAsync(p => p.Name == "نينوى");

            if (baghdad == null || basra == null || ninawa == null) return;

            var districts = new List<District>
            {
                // أقضية بغداد
                new District { Name = "الرصافة", ProvinceId = baghdad.Id, Description = "قضاء الرصافة في بغداد" },
                new District { Name = "الكرخ", ProvinceId = baghdad.Id, Description = "قضاء الكرخ في بغداد" },
                new District { Name = "الصدر", ProvinceId = baghdad.Id, Description = "قضاء الصدر في بغداد" },
                new District { Name = "المدائن", ProvinceId = baghdad.Id, Description = "قضاء المدائن في بغداد" },
                new District { Name = "أبو غريب", ProvinceId = baghdad.Id, Description = "قضاء أبو غريب في بغداد" },
                new District { Name = "الطارمية", ProvinceId = baghdad.Id, Description = "قضاء الطارمية في بغداد" },

                // أقضية البصرة
                new District { Name = "البصرة", ProvinceId = basra.Id, Description = "قضاء البصرة المركز" },
                new District { Name = "القرنة", ProvinceId = basra.Id, Description = "قضاء القرنة في البصرة" },
                new District { Name = "الزبير", ProvinceId = basra.Id, Description = "قضاء الزبير في البصرة" },
                new District { Name = "أبو الخصيب", ProvinceId = basra.Id, Description = "قضاء أبو الخصيب في البصرة" },
                new District { Name = "الفاو", ProvinceId = basra.Id, Description = "قضاء الفاو في البصرة" },

                // أقضية نينوى
                new District { Name = "الموصل", ProvinceId = ninawa.Id, Description = "قضاء الموصل المركز" },
                new District { Name = "تلعفر", ProvinceId = ninawa.Id, Description = "قضاء تلعفر في نينوى" },
                new District { Name = "سنجار", ProvinceId = ninawa.Id, Description = "قضاء سنجار في نينوى" },
                new District { Name = "الحمدانية", ProvinceId = ninawa.Id, Description = "قضاء الحمدانية في نينوى" },
                new District { Name = "الشيخان", ProvinceId = ninawa.Id, Description = "قضاء الشيخان في نينوى" }
            };

            context.Districts.AddRange(districts);
            await context.SaveChangesAsync();
        }

        public static async Task SeedSubdistrictsAsync(ApplicationDbContext context)
        {
            // التحقق من وجود نواحي
            if (await context.Subdistricts.AnyAsync())
            {
                return; // البيانات موجودة مسبقاً
            }

            // الحصول على بعض الأقضية
            var rasafa = await context.Districts.FirstOrDefaultAsync(d => d.Name == "الرصافة");
            var karkh = await context.Districts.FirstOrDefaultAsync(d => d.Name == "الكرخ");
            var basraCenter = await context.Districts.FirstOrDefaultAsync(d => d.Name == "البصرة");

            if (rasafa == null || karkh == null || basraCenter == null) return;

            var subdistricts = new List<Subdistrict>
            {
                // نواحي الرصافة
                new Subdistrict { Name = "الأعظمية", DistrictId = rasafa.Id, Description = "ناحية الأعظمية" },
                new Subdistrict { Name = "الصدر", DistrictId = rasafa.Id, Description = "ناحية الصدر" },
                new Subdistrict { Name = "النهروان", DistrictId = rasafa.Id, Description = "ناحية النهروان" },

                // نواحي الكرخ
                new Subdistrict { Name = "الكاظمية", DistrictId = karkh.Id, Description = "ناحية الكاظمية" },
                new Subdistrict { Name = "الشعلة", DistrictId = karkh.Id, Description = "ناحية الشعلة" },
                new Subdistrict { Name = "الحرية", DistrictId = karkh.Id, Description = "ناحية الحرية" },

                // نواحي البصرة
                new Subdistrict { Name = "الهارثة", DistrictId = basraCenter.Id, Description = "ناحية الهارثة" },
                new Subdistrict { Name = "شط العرب", DistrictId = basraCenter.Id, Description = "ناحية شط العرب" },
                new Subdistrict { Name = "الدير", DistrictId = basraCenter.Id, Description = "ناحية الدير" }
            };

            context.Subdistricts.AddRange(subdistricts);
            await context.SaveChangesAsync();
        }
    }
}
