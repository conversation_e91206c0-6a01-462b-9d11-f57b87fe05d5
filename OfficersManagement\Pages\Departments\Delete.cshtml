@page
@model OfficersManagement.Pages.Departments.DeleteModel
@{
    ViewData["Title"] = "حذف القسم";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-danger">
                    <i class="fas fa-trash me-2"></i>
                    حذف القسم
                </h2>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف القسم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هل أنت متأكد من حذف هذا القسم؟ هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات القسم:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الرقم:</strong></td>
                                    <td>@Model.Department.Id</td>
                                </tr>
                                <tr>
                                    <td><strong>الاسم:</strong></td>
                                    <td>@Model.Department.Name</td>
                                </tr>
                                <tr>
                                    <td><strong>المديرية:</strong></td>
                                    <td><span class="badge bg-info">@Model.Department.Directorate?.Name</span></td>
                                </tr>
                                <tr>
                                    <td><strong>الوكالة:</strong></td>
                                    <td><span class="badge bg-primary">@Model.Department.Directorate?.Agency?.Name</span></td>
                                </tr>
                                <tr>
                                    <td><strong>عدد الشعب:</strong></td>
                                    <td><span class="badge bg-warning">@Model.Department.Divisions.Count</span></td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        @if (Model.Department.IsActive)
                                        {
                                            <span class="badge bg-success">نشط</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">غير نشط</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if (Model.Department.Divisions.Any())
                    {
                        <div class="alert alert-danger mt-3">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>تنبيه:</strong> هذا القسم يحتوي على @Model.Department.Divisions.Count شعبة. حذف القسم سيؤثر على جميع الشعب التابعة له.
                        </div>
                    }

                    <form method="post" class="mt-4">
                        <input type="hidden" asp-for="Department.Id" />
                        <div class="d-flex justify-content-end gap-2">
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>
                                تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
