﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OfficersManagement.Migrations
{
    /// <inheritdoc />
    public partial class AddLocationFieldsToOfficer : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Alley",
                table: "Officers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DistrictId",
                table: "Officers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HouseNumber",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Locality",
                table: "Officers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NearestLandmark",
                table: "Officers",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Neighborhood",
                table: "Officers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "SubdistrictId",
                table: "Officers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "VillageId",
                table: "Officers",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Districts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ProvinceId = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Districts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Districts_Provinces_ProvinceId",
                        column: x => x.ProvinceId,
                        principalTable: "Provinces",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Subdistricts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    DistrictId = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Subdistricts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Subdistricts_Districts_DistrictId",
                        column: x => x.DistrictId,
                        principalTable: "Districts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Villages",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    SubdistrictId = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Villages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Villages_Subdistricts_SubdistrictId",
                        column: x => x.SubdistrictId,
                        principalTable: "Subdistricts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "Alley", "DistrictId", "HouseNumber", "Locality", "NearestLandmark", "Neighborhood", "SubdistrictId", "VillageId" },
                values: new object[] { null, null, null, null, null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "Alley", "DistrictId", "HouseNumber", "Locality", "NearestLandmark", "Neighborhood", "SubdistrictId", "VillageId" },
                values: new object[] { null, null, null, null, null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "Alley", "DistrictId", "HouseNumber", "Locality", "NearestLandmark", "Neighborhood", "SubdistrictId", "VillageId" },
                values: new object[] { null, null, null, null, null, null, null, null });

            migrationBuilder.CreateIndex(
                name: "IX_Officers_DistrictId",
                table: "Officers",
                column: "DistrictId");

            migrationBuilder.CreateIndex(
                name: "IX_Officers_SubdistrictId",
                table: "Officers",
                column: "SubdistrictId");

            migrationBuilder.CreateIndex(
                name: "IX_Officers_VillageId",
                table: "Officers",
                column: "VillageId");

            migrationBuilder.CreateIndex(
                name: "IX_Districts_ProvinceId",
                table: "Districts",
                column: "ProvinceId");

            migrationBuilder.CreateIndex(
                name: "IX_Subdistricts_DistrictId",
                table: "Subdistricts",
                column: "DistrictId");

            migrationBuilder.CreateIndex(
                name: "IX_Villages_SubdistrictId",
                table: "Villages",
                column: "SubdistrictId");

            migrationBuilder.AddForeignKey(
                name: "FK_Officers_Districts_DistrictId",
                table: "Officers",
                column: "DistrictId",
                principalTable: "Districts",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Officers_Subdistricts_SubdistrictId",
                table: "Officers",
                column: "SubdistrictId",
                principalTable: "Subdistricts",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Officers_Villages_VillageId",
                table: "Officers",
                column: "VillageId",
                principalTable: "Villages",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Officers_Districts_DistrictId",
                table: "Officers");

            migrationBuilder.DropForeignKey(
                name: "FK_Officers_Subdistricts_SubdistrictId",
                table: "Officers");

            migrationBuilder.DropForeignKey(
                name: "FK_Officers_Villages_VillageId",
                table: "Officers");

            migrationBuilder.DropTable(
                name: "Villages");

            migrationBuilder.DropTable(
                name: "Subdistricts");

            migrationBuilder.DropTable(
                name: "Districts");

            migrationBuilder.DropIndex(
                name: "IX_Officers_DistrictId",
                table: "Officers");

            migrationBuilder.DropIndex(
                name: "IX_Officers_SubdistrictId",
                table: "Officers");

            migrationBuilder.DropIndex(
                name: "IX_Officers_VillageId",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "Alley",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "DistrictId",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "HouseNumber",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "Locality",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "NearestLandmark",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "Neighborhood",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "SubdistrictId",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "VillageId",
                table: "Officers");
        }
    }
}
