@page
@model OfficersManagement.Pages.Officers.CreateModel
@{
    ViewData["Title"] = "إضافة ضابط جديد";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة ضابط جديد
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        


                        <div class="row">
                            <!-- معلومة عن رقم الموظف -->
                            <div class="col-12 mb-3">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> سيتم توليد رقم الموظف تلقائياً عند الحفظ
                                </div>
                            </div>
                        </div>

                        <!-- الصورة الشخصية -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-image me-2"></i>
                                    الصورة الشخصية
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <!-- Hidden File Input -->
                                <input type="file" id="photoFile" name="PhotoFile" accept="image/*" style="display: none;" />

                                <!-- Photo Display Area -->
                                <div id="photoDisplayArea" class="photo-upload-area" onclick="selectPhoto()">
                                    <div id="photoPlaceholder" class="photo-placeholder">
                                        <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">اضغط لاختيار صورة</h5>
                                        <p class="text-muted small">JPG, PNG - حد أقصى 5 ميجابايت</p>
                                    </div>
                                    <img id="previewImage" src="" alt="الصورة الشخصية" class="photo-preview" style="display: none;" />
                                </div>

                                <!-- Photo Action Buttons -->
                                <div class="mt-3">
                                    <button type="button" id="selectFileBtn" class="btn btn-primary btn-sm" onclick="selectPhoto()">
                                        <i class="fas fa-folder-open"></i> اختيار من الملفات
                                    </button>
                                    <button type="button" id="captureBtn" class="btn btn-success btn-sm ms-2" onclick="openCamera()">
                                        <i class="fas fa-camera"></i> التقاط بالكاميرا
                                    </button>
                                </div>

                                <!-- Photo Actions (when photo exists) -->
                                <div id="photoActions" style="display: none;" class="mt-3">
                                    <button type="button" id="removePhoto" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash"></i> إزالة الصورة
                                    </button>
                                    <button type="button" id="changePhoto" class="btn btn-outline-primary btn-sm ms-2" onclick="selectPhoto()">
                                        <i class="fas fa-edit"></i> تغيير الصورة
                                    </button>
                                    <button type="button" id="retakePhoto" class="btn btn-outline-success btn-sm ms-2" onclick="openCamera()">
                                        <i class="fas fa-camera-retro"></i> التقاط جديد
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Camera Modal -->
                        <div class="modal fade" id="cameraModal" tabindex="-1" aria-labelledby="cameraModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-md">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="cameraModalLabel">
                                            <i class="fas fa-camera me-2"></i>
                                            التقاط صورة بالكاميرا
                                        </h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body text-center">
                                        <!-- Camera Selection -->
                                        <div class="mb-3">
                                            <label for="cameraSelect" class="form-label">اختيار الكاميرا:</label>
                                            <select id="cameraSelect" class="form-select">
                                                <option value="">جاري تحميل الكاميرات...</option>
                                            </select>
                                        </div>

                                        <!-- Camera Preview Area -->
                                        <div class="camera-container mb-3">
                                            <video id="cameraPreview" width="320" height="240" autoplay muted style="border: 2px solid #dee2e6; border-radius: 10px; display: none;"></video>
                                            <canvas id="photoCanvas" width="320" height="240" style="border: 2px solid #28a745; border-radius: 10px; display: none;"></canvas>
                                            <div id="cameraPlaceholder" class="camera-placeholder">
                                                <i class="fas fa-video fa-2x text-muted mb-3"></i>
                                                <p class="text-muted">اختر كاميرا وانقر "تشغيل الكاميرا"</p>
                                            </div>
                                        </div>

                                        <!-- Camera Controls -->
                                        <div class="d-flex justify-content-center gap-2">
                                            <button type="button" id="startCamera" class="btn btn-primary">
                                                <i class="fas fa-play"></i> تشغيل الكاميرا
                                            </button>
                                            <button type="button" id="capturePhoto" class="btn btn-success" disabled>
                                                <i class="fas fa-camera-retro"></i> التقاط صورة
                                            </button>
                                            <button type="button" id="retakeBtn" class="btn btn-warning" style="display: none;">
                                                <i class="fas fa-redo"></i> إعادة التقاط
                                            </button>
                                            <button type="button" id="stopCamera" class="btn btn-danger" disabled>
                                                <i class="fas fa-stop"></i> إيقاف الكاميرا
                                            </button>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                        <button type="button" id="usePhoto" class="btn btn-success" style="display: none;">
                                            <i class="fas fa-check"></i> استخدام هذه الصورة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الرقم الإحصائي -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Officer.StatisticalNumber" class="form-label"></label>
                                <input asp-for="Officer.StatisticalNumber" class="form-control" placeholder="أدخل الرقم الإحصائي" />
                                <span asp-validation-for="Officer.StatisticalNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- الرتبة -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Officer.RankId" class="form-label">الرتبة</label>
                                <select asp-for="Officer.RankId" asp-items="Model.RanksList" class="form-select">
                                    <option value="">اختر الرتبة</option>
                                </select>
                                <span asp-validation-for="Officer.RankId" class="text-danger"></span>
                                <div class="form-text">
                                    <a asp-page="/Ranks/Create" target="_blank" class="text-decoration-none">
                                        <i class="fas fa-plus me-1"></i>
                                        إضافة رتبة جديدة
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Officer.Title" class="form-label"></label>
                                <input asp-for="Officer.Title" class="form-control" placeholder="أدخل اللقب" />
                                <span asp-validation-for="Officer.Title" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- الاسم الرباعي -->
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label asp-for="Officer.FirstName" class="form-label"></label>
                                <input asp-for="Officer.FirstName" class="form-control" placeholder="أدخل الاسم الأول" />
                                <span asp-validation-for="Officer.FirstName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Officer.SecondName" class="form-label"></label>
                                <input asp-for="Officer.SecondName" class="form-control" placeholder="أدخل الاسم الثاني" />
                                <span asp-validation-for="Officer.SecondName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Officer.ThirdName" class="form-label"></label>
                                <input asp-for="Officer.ThirdName" class="form-control" placeholder="أدخل الاسم الثالث" />
                                <span asp-validation-for="Officer.ThirdName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Officer.FourthName" class="form-label"></label>
                                <input asp-for="Officer.FourthName" class="form-control" placeholder="أدخل الاسم الرابع" />
                                <span asp-validation-for="Officer.FourthName" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- محل الولادة وتاريخ الولادة -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Officer.PlaceOfBirth" class="form-label"></label>
                                <input asp-for="Officer.PlaceOfBirth" class="form-control" />
                                <span asp-validation-for="Officer.PlaceOfBirth" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Officer.DateOfBirth" class="form-label"></label>
                                <input asp-for="Officer.DateOfBirth" class="form-control" type="date" />
                                <span asp-validation-for="Officer.DateOfBirth" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- الحالة الاجتماعية وفصيلة الدم والحالة الصحية -->
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="Officer.MaritalStatus" class="form-label"></label>
                                <select asp-for="Officer.MaritalStatus" class="form-select">
                                    <option value="">اختر الحالة الاجتماعية</option>
                                    <option value="أعزب">أعزب</option>
                                    <option value="متزوج">متزوج</option>
                                    <option value="مطلق">مطلق</option>
                                    <option value="أرمل">أرمل</option>
                                </select>
                                <span asp-validation-for="Officer.MaritalStatus" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Officer.BloodType" class="form-label"></label>
                                <select asp-for="Officer.BloodType" class="form-select">
                                    <option value="">اختر فصيلة الدم</option>
                                    <option value="A+">A+</option>
                                    <option value="A-">A-</option>
                                    <option value="B+">B+</option>
                                    <option value="B-">B-</option>
                                    <option value="AB+">AB+</option>
                                    <option value="AB-">AB-</option>
                                    <option value="O+">O+</option>
                                    <option value="O-">O-</option>
                                </select>
                                <span asp-validation-for="Officer.BloodType" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Officer.HealthStatus" class="form-label"></label>
                                <select asp-for="Officer.HealthStatus" class="form-select">
                                    <option value="">اختر الحالة الصحية</option>
                                    <option value="سليم">سليم</option>
                                    <option value="مريض">مريض</option>
                                    <option value="معاق">معاق</option>
                                    <option value="مصاب في الخدمة">مصاب في الخدمة</option>
                                    <option value="تحت العلاج">تحت العلاج</option>
                                </select>
                                <span asp-validation-for="Officer.HealthStatus" class="text-danger"></span>
                            </div>
                        </div>



                        <!-- معلومات السكن -->
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    معلومات السكن
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- المحافظة والقضاء والناحية -->
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.ProvinceId" class="form-label">المحافظة</label>
                                        <select asp-for="Officer.ProvinceId" asp-items="Model.ProvincesList" class="form-select">
                                            <option value="">اختر المحافظة</option>
                                        </select>
                                        <span asp-validation-for="Officer.ProvinceId" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.District" class="form-label">القضاء</label>
                                        <input asp-for="Officer.District" class="form-control" placeholder="أدخل اسم القضاء" />
                                        <span asp-validation-for="Officer.District" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.Subdistrict" class="form-label">الناحية</label>
                                        <input asp-for="Officer.Subdistrict" class="form-control" placeholder="أدخل اسم الناحية" />
                                        <span asp-validation-for="Officer.Subdistrict" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- القرية والحي والمحلة -->
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.Village" class="form-label">القرية</label>
                                        <input asp-for="Officer.Village" class="form-control" placeholder="أدخل اسم القرية" />
                                        <span asp-validation-for="Officer.Village" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.Neighborhood" class="form-label">الحي</label>
                                        <input asp-for="Officer.Neighborhood" class="form-control" placeholder="أدخل اسم الحي" />
                                        <span asp-validation-for="Officer.Neighborhood" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.Locality" class="form-label">المحلة</label>
                                        <input asp-for="Officer.Locality" class="form-control" placeholder="أدخل اسم المحلة" />
                                        <span asp-validation-for="Officer.Locality" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- الزقاق ورقم الدار وأقرب نقطة دالة -->
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.Alley" class="form-label">الزقاق</label>
                                        <input asp-for="Officer.Alley" class="form-control" placeholder="أدخل اسم الزقاق" />
                                        <span asp-validation-for="Officer.Alley" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.HouseNumber" class="form-label">رقم الدار</label>
                                        <input asp-for="Officer.HouseNumber" class="form-control" placeholder="أدخل رقم الدار" />
                                        <span asp-validation-for="Officer.HouseNumber" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.NearestLandmark" class="form-label">أقرب نقطة دالة</label>
                                        <input asp-for="Officer.NearestLandmark" class="form-control" placeholder="أدخل أقرب نقطة دالة" />
                                        <span asp-validation-for="Officer.NearestLandmark" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- معاينة العنوان بالتفصيل -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <h6 class="alert-heading">
                                                <i class="fas fa-eye me-2"></i>
                                                معاينة العنوان بالتفصيل
                                            </h6>
                                            <p class="mb-2">
                                                <small>سيتم تكوين العنوان بالتفصيل تلقائياً من البيانات المدخلة أعلاه</small>
                                            </p>
                                            <div class="bg-white p-2 rounded border">
                                                <strong>العنوان بالتفصيل:</strong>
                                                <div id="addressPreview" class="mt-1 text-primary fw-bold">
                                                    سيظهر العنوان هنا عند ملء البيانات...
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-page="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للقائمة
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <style>
        .photo-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 30px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .photo-upload-area:hover {
            border-color: #007bff;
            background-color: #e3f2fd;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,123,255,0.1);
        }

        .photo-placeholder {
            text-align: center;
        }

        .photo-preview {
            max-width: 100%;
            max-height: 200px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            object-fit: cover;
        }

        .photo-upload-area.has-image {
            border: 2px solid #28a745;
            background-color: #f8fff9;
            padding: 10px;
        }

        .photo-upload-area.has-image:hover {
            border-color: #20c997;
            background-color: #e8f5e8;
        }

        .camera-container {
            min-height: 260px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            border-radius: 10px;
            border: 2px dashed #dee2e6;
        }

        .camera-placeholder {
            text-align: center;
            padding: 30px;
        }
    </style>

    <script>
        // تحديد الحد الأقصى لتاريخ الولادة (اليوم)
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.querySelector('input[type="date"]');
            if (dateInput) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.setAttribute('max', today);
            }

            // إعداد تحديث معاينة العنوان
            setupAddressPreview();

            // إعداد وظائف رفع الصورة
            setupPhotoUpload();

            // إعداد وظائف الكاميرا
            setupCamera();
        });

        function setupAddressPreview() {
            // إضافة مستمعين للأحداث لجميع حقول العنوان
            const addressFields = [
                '[name="Officer.ProvinceId"]',
                '[name="Officer.District"]',
                '[name="Officer.Subdistrict"]',
                '[name="Officer.Village"]',
                '[name="Officer.Neighborhood"]',
                '[name="Officer.Locality"]',
                '[name="Officer.Alley"]',
                '[name="Officer.HouseNumber"]',
                '[name="Officer.NearestLandmark"]'
            ];

            addressFields.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    element.addEventListener('input', updateAddressPreview);
                    element.addEventListener('change', updateAddressPreview);
                }
            });

            // تحديث أولي
            updateAddressPreview();
        }

        function updateAddressPreview() {
            const addressParts = [];

            // المحافظة
            const provinceSelect = document.querySelector('[name="Officer.ProvinceId"]');
            if (provinceSelect && provinceSelect.value) {
                const selectedOption = provinceSelect.options[provinceSelect.selectedIndex];
                if (selectedOption && selectedOption.text !== 'اختر المحافظة') {
                    addressParts.push(`محافظة ${selectedOption.text}`);
                }
            }

            // القضاء
            const district = document.querySelector('[name="Officer.District"]')?.value?.trim();
            if (district) {
                addressParts.push(`قضاء ${district}`);
            }

            // الناحية
            const subdistrict = document.querySelector('[name="Officer.Subdistrict"]')?.value?.trim();
            if (subdistrict) {
                addressParts.push(`ناحية ${subdistrict}`);
            }

            // القرية
            const village = document.querySelector('[name="Officer.Village"]')?.value?.trim();
            if (village) {
                addressParts.push(`قرية ${village}`);
            }

            // الحي
            const neighborhood = document.querySelector('[name="Officer.Neighborhood"]')?.value?.trim();
            if (neighborhood) {
                addressParts.push(`حي ${neighborhood}`);
            }

            // المحلة
            const locality = document.querySelector('[name="Officer.Locality"]')?.value?.trim();
            if (locality) {
                addressParts.push(`محلة ${locality}`);
            }

            // الزقاق
            const alley = document.querySelector('[name="Officer.Alley"]')?.value?.trim();
            if (alley) {
                addressParts.push(`زقاق ${alley}`);
            }

            // رقم الدار
            const houseNumber = document.querySelector('[name="Officer.HouseNumber"]')?.value?.trim();
            if (houseNumber) {
                addressParts.push(`دار رقم ${houseNumber}`);
            }

            // أقرب نقطة دالة
            const landmark = document.querySelector('[name="Officer.NearestLandmark"]')?.value?.trim();
            if (landmark) {
                addressParts.push(`قرب ${landmark}`);
            }

            const addressPreview = document.getElementById('addressPreview');
            if (addressPreview) {
                if (addressParts.length > 0) {
                    addressPreview.textContent = addressParts.join(' - ');
                    addressPreview.className = 'mt-2 text-primary fw-bold';
                } else {
                    addressPreview.textContent = 'سيظهر العنوان هنا عند ملء البيانات...';
                    addressPreview.className = 'mt-2 text-muted';
                }
            }
        }

        // Photo upload functionality
        function setupPhotoUpload() {
            const photoFile = document.getElementById('photoFile');
            const photoDisplayArea = document.getElementById('photoDisplayArea');
            const photoPlaceholder = document.getElementById('photoPlaceholder');
            const previewImage = document.getElementById('previewImage');
            const photoActions = document.getElementById('photoActions');
            const removePhoto = document.getElementById('removePhoto');

            // Event listeners
            photoFile.addEventListener('change', handleFileUpload);
            removePhoto.addEventListener('click', clearPhoto);

            function handleFileUpload(event) {
                const file = event.target.files[0];
                if (!file) return;

                // Validate file type
                if (!file.type.startsWith('image/')) {
                    alert('يرجى اختيار ملف صورة صحيح');
                    photoFile.value = '';
                    return;
                }

                // Validate file size (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
                    photoFile.value = '';
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    showPhotoPreview(e.target.result);
                };
                reader.readAsDataURL(file);
            }

            function showPhotoPreview(dataUrl) {
                previewImage.src = dataUrl;
                previewImage.style.display = 'block';
                photoPlaceholder.style.display = 'none';
                photoActions.style.display = 'block';
                photoDisplayArea.classList.add('has-image');
            }

            function clearPhoto() {
                previewImage.style.display = 'none';
                previewImage.src = '';
                photoPlaceholder.style.display = 'block';
                photoActions.style.display = 'none';
                photoDisplayArea.classList.remove('has-image');
                photoFile.value = '';

                // Remove hidden input for camera data
                const hiddenInput = document.getElementById('photoDataInput');
                if (hiddenInput) {
                    hiddenInput.remove();
                }
            }
        }

        // Global function to select photo
        function selectPhoto() {
            document.getElementById('photoFile').click();
        }

        // Global function to open camera
        function openCamera() {
            const cameraModal = new bootstrap.Modal(document.getElementById('cameraModal'));
            cameraModal.show();
            loadCameras();
        }

        // Camera functionality
        let currentStream = null;
        let capturedImageData = null;

        function setupCamera() {
            const startCamera = document.getElementById('startCamera');
            const capturePhoto = document.getElementById('capturePhoto');
            const retakeBtn = document.getElementById('retakeBtn');
            const stopCamera = document.getElementById('stopCamera');
            const usePhoto = document.getElementById('usePhoto');
            const cameraModal = document.getElementById('cameraModal');

            // Event listeners
            startCamera.addEventListener('click', startCameraStream);
            capturePhoto.addEventListener('click', capturePhotoFromCamera);
            retakeBtn.addEventListener('click', retakePhoto);
            stopCamera.addEventListener('click', stopCameraStream);
            usePhoto.addEventListener('click', useCapturedPhoto);

            // Clean up when modal is closed
            cameraModal.addEventListener('hidden.bs.modal', function () {
                stopCameraStream();
                resetCameraUI();
            });
        }

        async function loadCameras() {
            const cameraSelect = document.getElementById('cameraSelect');
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');

                cameraSelect.innerHTML = '';
                if (videoDevices.length === 0) {
                    cameraSelect.innerHTML = '<option value="">لا توجد كاميرات متاحة</option>';
                    document.getElementById('startCamera').disabled = true;
                    return;
                }

                videoDevices.forEach((device, index) => {
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.text = device.label || `كاميرا ${index + 1}`;
                    cameraSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading cameras:', error);
                cameraSelect.innerHTML = '<option value="">خطأ في تحميل الكاميرات</option>';
                document.getElementById('startCamera').disabled = true;
            }
        }

        async function startCameraStream() {
            const cameraSelect = document.getElementById('cameraSelect');
            const deviceId = cameraSelect.value;

            if (!deviceId) {
                alert('يرجى اختيار كاميرا أولاً');
                return;
            }

            try {
                const constraints = {
                    video: {
                        deviceId: { exact: deviceId },
                        width: { ideal: 320 },
                        height: { ideal: 240 }
                    }
                };

                currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                const cameraPreview = document.getElementById('cameraPreview');
                const cameraPlaceholder = document.getElementById('cameraPlaceholder');

                cameraPreview.srcObject = currentStream;
                cameraPreview.style.display = 'block';
                cameraPlaceholder.style.display = 'none';

                // Update UI
                document.getElementById('startCamera').disabled = true;
                document.getElementById('capturePhoto').disabled = false;
                document.getElementById('stopCamera').disabled = false;
                cameraSelect.disabled = true;
            } catch (error) {
                console.error('Error starting camera:', error);
                alert('خطأ في تشغيل الكاميرا: ' + error.message);
            }
        }

        function capturePhotoFromCamera() {
            const cameraPreview = document.getElementById('cameraPreview');
            const photoCanvas = document.getElementById('photoCanvas');
            const context = photoCanvas.getContext('2d');

            // Set canvas size to match video
            photoCanvas.width = cameraPreview.videoWidth;
            photoCanvas.height = cameraPreview.videoHeight;

            // Draw the current frame to canvas
            context.drawImage(cameraPreview, 0, 0);

            // Get image data
            capturedImageData = photoCanvas.toDataURL('image/jpeg', 0.8);

            // Show captured photo
            cameraPreview.style.display = 'none';
            photoCanvas.style.display = 'block';

            // Update UI
            document.getElementById('capturePhoto').disabled = true;
            document.getElementById('retakeBtn').style.display = 'inline-block';
            document.getElementById('usePhoto').style.display = 'inline-block';
        }

        function retakePhoto() {
            const cameraPreview = document.getElementById('cameraPreview');
            const photoCanvas = document.getElementById('photoCanvas');

            // Show camera preview again
            cameraPreview.style.display = 'block';
            photoCanvas.style.display = 'none';

            // Reset captured data
            capturedImageData = null;

            // Update UI
            document.getElementById('capturePhoto').disabled = false;
            document.getElementById('retakeBtn').style.display = 'none';
            document.getElementById('usePhoto').style.display = 'none';
        }

        function stopCameraStream() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
            }

            const cameraPreview = document.getElementById('cameraPreview');
            const photoCanvas = document.getElementById('photoCanvas');
            const cameraPlaceholder = document.getElementById('cameraPlaceholder');

            cameraPreview.style.display = 'none';
            photoCanvas.style.display = 'none';
            cameraPlaceholder.style.display = 'block';

            resetCameraUI();
        }

        function resetCameraUI() {
            const cameraSelect = document.getElementById('cameraSelect');

            document.getElementById('startCamera').disabled = false;
            document.getElementById('capturePhoto').disabled = true;
            document.getElementById('stopCamera').disabled = true;
            document.getElementById('retakeBtn').style.display = 'none';
            document.getElementById('usePhoto').style.display = 'none';
            cameraSelect.disabled = false;
            capturedImageData = null;
        }

        function useCapturedPhoto() {
            if (capturedImageData) {
                // Show the captured photo in the main preview
                const previewImage = document.getElementById('previewImage');
                const photoPlaceholder = document.getElementById('photoPlaceholder');
                const photoActions = document.getElementById('photoActions');
                const photoDisplayArea = document.getElementById('photoDisplayArea');

                previewImage.src = capturedImageData;
                previewImage.style.display = 'block';
                photoPlaceholder.style.display = 'none';
                photoActions.style.display = 'block';
                photoDisplayArea.classList.add('has-image');

                // Create a hidden input with the image data
                let hiddenInput = document.getElementById('photoDataInput');
                if (!hiddenInput) {
                    hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.id = 'photoDataInput';
                    hiddenInput.name = 'PhotoDataUrl';
                    document.querySelector('form').appendChild(hiddenInput);
                }
                hiddenInput.value = capturedImageData;

                // Close the modal
                const cameraModal = bootstrap.Modal.getInstance(document.getElementById('cameraModal'));
                cameraModal.hide();
            }
        }

    </script>
}
