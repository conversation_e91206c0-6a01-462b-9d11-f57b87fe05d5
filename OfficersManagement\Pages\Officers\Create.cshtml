@page
@model OfficersManagement.Pages.Officers.CreateModel
@{
    ViewData["Title"] = "إضافة ضابط جديد";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة ضابط جديد
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>



                        <div class="row">
                            <!-- معلومة عن رقم الموظف -->
                            <div class="col-12 mb-3">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> سيتم توليد رقم الموظف تلقائياً عند الحفظ
                                </div>
                            </div>
                        </div>

                        <!-- الصورة الشخصية -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-image me-2"></i>
                                    الصورة الشخصية
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <!-- Hidden File Input -->
                                <input type="file" id="photoFile" name="PhotoFile" accept="image/*" style="display: none;" />

                                <!-- Photo Display Area -->
                                <div id="photoDisplayArea" class="photo-upload-area" onclick="selectPhoto()">
                                    <div id="photoPlaceholder" class="photo-placeholder">
                                        <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">اضغط لاختيار صورة</h5>
                                        <p class="text-muted small">JPG, PNG - حد أقصى 5 ميجابايت</p>
                                    </div>
                                    <img id="previewImage" src="" alt="الصورة الشخصية" class="photo-preview" style="display: none;" />
                                </div>

                                <!-- Photo Action Buttons -->
                                <div class="mt-3">
                                    <button type="button" id="selectFileBtn" class="btn btn-primary btn-sm" onclick="selectPhoto()">
                                        <i class="fas fa-folder-open"></i> اختيار من الملفات
                                    </button>
                                    <button type="button" id="captureBtn" class="btn btn-success btn-sm ms-2" onclick="openCamera()">
                                        <i class="fas fa-camera"></i> التقاط بالكاميرا
                                    </button>
                                </div>

                                <!-- Photo Actions (when photo exists) -->
                                <div id="photoActions" style="display: none;" class="mt-3">
                                    <button type="button" id="removePhoto" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash"></i> إزالة الصورة
                                    </button>
                                    <button type="button" id="changePhoto" class="btn btn-outline-primary btn-sm ms-2" onclick="selectPhoto()">
                                        <i class="fas fa-edit"></i> تغيير الصورة
                                    </button>
                                    <button type="button" id="retakePhoto" class="btn btn-outline-success btn-sm ms-2" onclick="openCamera()">
                                        <i class="fas fa-camera-retro"></i> التقاط جديد
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- نظام التابات -->
                        <div class="card mb-4">
                            <div class="card-header p-0">
                                <ul class="nav nav-tabs card-header-tabs" id="officerTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab" data-bs-target="#basic-info" type="button" role="tab" aria-controls="basic-info" aria-selected="true">
                                            <i class="fas fa-user me-2"></i>
                                            المعلومات الأساسية
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="job-info-tab" data-bs-toggle="tab" data-bs-target="#job-info" type="button" role="tab" aria-controls="job-info" aria-selected="false">
                                            <i class="fas fa-briefcase me-2"></i>
                                            معلومات الوظيفة
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content" id="officerTabsContent">
                                    <!-- تاب المعلومات الأساسية -->
                                    <div class="tab-pane fade show active" id="basic-info" role="tabpanel" aria-labelledby="basic-info-tab">
                                        <!-- الرقم الإحصائي -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Officer.StatisticalNumber" class="form-label"></label>
                                <input asp-for="Officer.StatisticalNumber" class="form-control" placeholder="أدخل الرقم الإحصائي" />
                                <span asp-validation-for="Officer.StatisticalNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- الرتبة -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Officer.RankId" class="form-label">الرتبة</label>
                                <select asp-for="Officer.RankId" asp-items="Model.RanksList" class="form-select">
                                    <option value="">اختر الرتبة</option>
                                </select>
                                <span asp-validation-for="Officer.RankId" class="text-danger"></span>
                                <div class="form-text">
                                    <a asp-page="/Ranks/Create" target="_blank" class="text-decoration-none">
                                        <i class="fas fa-plus me-1"></i>
                                        إضافة رتبة جديدة
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Officer.Title" class="form-label"></label>
                                <input asp-for="Officer.Title" class="form-control" placeholder="أدخل اللقب" />
                                <span asp-validation-for="Officer.Title" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- الاسم الرباعي -->
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label asp-for="Officer.FirstName" class="form-label"></label>
                                <input asp-for="Officer.FirstName" class="form-control" placeholder="أدخل الاسم الأول" />
                                <span asp-validation-for="Officer.FirstName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Officer.SecondName" class="form-label"></label>
                                <input asp-for="Officer.SecondName" class="form-control" placeholder="أدخل الاسم الثاني" />
                                <span asp-validation-for="Officer.SecondName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Officer.ThirdName" class="form-label"></label>
                                <input asp-for="Officer.ThirdName" class="form-control" placeholder="أدخل الاسم الثالث" />
                                <span asp-validation-for="Officer.ThirdName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Officer.FourthName" class="form-label"></label>
                                <input asp-for="Officer.FourthName" class="form-control" placeholder="أدخل الاسم الرابع" />
                                <span asp-validation-for="Officer.FourthName" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- محل الولادة وتاريخ الولادة -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Officer.PlaceOfBirth" class="form-label"></label>
                                <input asp-for="Officer.PlaceOfBirth" class="form-control" />
                                <span asp-validation-for="Officer.PlaceOfBirth" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Officer.DateOfBirth" class="form-label"></label>
                                <input asp-for="Officer.DateOfBirth" class="form-control" type="date" />
                                <span asp-validation-for="Officer.DateOfBirth" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- الحالة الاجتماعية وفصيلة الدم والحالة الصحية -->
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="Officer.MaritalStatus" class="form-label"></label>
                                <select asp-for="Officer.MaritalStatus" class="form-select">
                                    <option value="">اختر الحالة الاجتماعية</option>
                                    <option value="أعزب">أعزب</option>
                                    <option value="متزوج">متزوج</option>
                                    <option value="مطلق">مطلق</option>
                                    <option value="أرمل">أرمل</option>
                                </select>
                                <span asp-validation-for="Officer.MaritalStatus" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Officer.BloodType" class="form-label"></label>
                                <select asp-for="Officer.BloodType" class="form-select">
                                    <option value="">اختر فصيلة الدم</option>
                                    <option value="A+">A+</option>
                                    <option value="A-">A-</option>
                                    <option value="B+">B+</option>
                                    <option value="B-">B-</option>
                                    <option value="AB+">AB+</option>
                                    <option value="AB-">AB-</option>
                                    <option value="O+">O+</option>
                                    <option value="O-">O-</option>
                                </select>
                                <span asp-validation-for="Officer.BloodType" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Officer.HealthStatus" class="form-label"></label>
                                <select asp-for="Officer.HealthStatus" class="form-select">
                                    <option value="">اختر الحالة الصحية</option>
                                    <option value="سليم">سليم</option>
                                    <option value="مريض">مريض</option>
                                    <option value="معاق">معاق</option>
                                    <option value="مصاب في الخدمة">مصاب في الخدمة</option>
                                    <option value="تحت العلاج">تحت العلاج</option>
                                </select>
                                <span asp-validation-for="Officer.HealthStatus" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- معلومات السكن -->
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    معلومات السكن
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- المحافظة والقضاء والناحية -->
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.ProvinceId" class="form-label">المحافظة</label>
                                        <select asp-for="Officer.ProvinceId" asp-items="Model.ProvincesList" class="form-select">
                                            <option value="">اختر المحافظة</option>
                                        </select>
                                        <span asp-validation-for="Officer.ProvinceId" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.District" class="form-label">القضاء</label>
                                        <input asp-for="Officer.District" class="form-control" placeholder="أدخل اسم القضاء" />
                                        <span asp-validation-for="Officer.District" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.Subdistrict" class="form-label">الناحية</label>
                                        <input asp-for="Officer.Subdistrict" class="form-control" placeholder="أدخل اسم الناحية" />
                                        <span asp-validation-for="Officer.Subdistrict" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- القرية والحي والمحلة -->
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.Village" class="form-label">القرية</label>
                                        <input asp-for="Officer.Village" class="form-control" placeholder="أدخل اسم القرية" />
                                        <span asp-validation-for="Officer.Village" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.Neighborhood" class="form-label">الحي</label>
                                        <input asp-for="Officer.Neighborhood" class="form-control" placeholder="أدخل اسم الحي" />
                                        <span asp-validation-for="Officer.Neighborhood" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.Locality" class="form-label">المحلة</label>
                                        <input asp-for="Officer.Locality" class="form-control" placeholder="أدخل اسم المحلة" />
                                        <span asp-validation-for="Officer.Locality" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- الزقاق ورقم الدار وأقرب نقطة دالة -->
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.Alley" class="form-label">الزقاق</label>
                                        <input asp-for="Officer.Alley" class="form-control" placeholder="أدخل اسم الزقاق" />
                                        <span asp-validation-for="Officer.Alley" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.HouseNumber" class="form-label">رقم الدار</label>
                                        <input asp-for="Officer.HouseNumber" class="form-control" placeholder="أدخل رقم الدار" />
                                        <span asp-validation-for="Officer.HouseNumber" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label asp-for="Officer.NearestLandmark" class="form-label">أقرب نقطة دالة</label>
                                        <input asp-for="Officer.NearestLandmark" class="form-control" placeholder="أدخل أقرب نقطة دالة" />
                                        <span asp-validation-for="Officer.NearestLandmark" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- معاينة العنوان بالتفصيل -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <h6 class="alert-heading">
                                                <i class="fas fa-eye me-2"></i>
                                                معاينة العنوان بالتفصيل
                                            </h6>
                                            <p class="mb-2">
                                                <small>سيتم تكوين العنوان بالتفصيل تلقائياً من البيانات المدخلة أعلاه</small>
                                            </p>
                                            <div class="bg-white p-2 rounded border">
                                                <strong>العنوان بالتفصيل:</strong>
                                                <div id="addressPreview" class="mt-1 text-primary fw-bold">
                                                    سيظهر العنوان هنا عند ملء البيانات...
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                    </div>

                                    <!-- تاب معلومات الوظيفة -->
                                    <div class="tab-pane fade" id="job-info" role="tabpanel" aria-labelledby="job-info-tab">
                                        <!-- معلومات الوظيفة -->
                                        <div class="card mb-4">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-building me-2"></i>
                                                    معلومات الوظيفة
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <!-- الوكالة والمديرية -->
                                                <div class="row">
                                                    <div class="col-md-6 mb-3">
                                                        <label asp-for="Officer.AgencyId" class="form-label">الوكالة</label>
                                                        <select asp-for="Officer.AgencyId" asp-items="Model.AgenciesList" class="form-select" id="agencySelect" onchange="loadDirectorates()">
                                                            <option value="">اختر الوكالة</option>
                                                        </select>
                                                        <span asp-validation-for="Officer.AgencyId" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <label asp-for="Officer.DirectorateId" class="form-label">المديرية</label>
                                                        <select asp-for="Officer.DirectorateId" class="form-select" id="directorateSelect" onchange="loadDepartments()" disabled>
                                                            <option value="">اختر المديرية أولاً</option>
                                                        </select>
                                                        <span asp-validation-for="Officer.DirectorateId" class="text-danger"></span>
                                                    </div>
                                                </div>

                                                <!-- القسم والشعبة -->
                                                <div class="row">
                                                    <div class="col-md-6 mb-3">
                                                        <label asp-for="Officer.DepartmentId" class="form-label">القسم</label>
                                                        <select asp-for="Officer.DepartmentId" class="form-select" id="departmentSelect" onchange="loadDivisions()" disabled>
                                                            <option value="">اختر القسم أولاً</option>
                                                        </select>
                                                        <span asp-validation-for="Officer.DepartmentId" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <label asp-for="Officer.DivisionId" class="form-label">الشعبة</label>
                                                        <select asp-for="Officer.DivisionId" class="form-select" id="divisionSelect" disabled>
                                                            <option value="">اختر الشعبة أولاً</option>
                                                        </select>
                                                        <span asp-validation-for="Officer.DivisionId" class="text-danger"></span>
                                                    </div>
                                                </div>

                                                <!-- معاينة الهيكل الوظيفي -->
                                                <div class="row mt-4">
                                                    <div class="col-12">
                                                        <div class="alert alert-success">
                                                            <h6 class="alert-heading">
                                                                <i class="fas fa-sitemap me-2"></i>
                                                                معاينة الهيكل الوظيفي
                                                            </h6>
                                                            <p class="mb-2">
                                                                <small>سيتم عرض الهيكل الوظيفي المختار هنا</small>
                                                            </p>
                                                            <div class="bg-white p-3 rounded border">
                                                                <div id="jobStructurePreview" class="text-success fw-bold">
                                                                    <i class="fas fa-info-circle me-2"></i>
                                                                    يرجى اختيار الوكالة لبدء التنقل في الهيكل الوظيفي
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-page="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للقائمة
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Camera Modal -->
<div class="modal fade" id="cameraModal" tabindex="-1" aria-labelledby="cameraModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cameraModalLabel">
                    <i class="fas fa-camera me-2"></i>
                    التقاط صورة بالكاميرا
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <!-- Camera Selection -->
                <div class="mb-3">
                    <label for="cameraSelect" class="form-label">اختيار الكاميرا:</label>
                    <select id="cameraSelect" class="form-select" onchange="switchCamera()">
                        <option value="">جاري البحث عن الكاميرات...</option>
                    </select>
                </div>

                <!-- Camera Status -->
                <div id="cameraStatus" class="alert alert-info" style="display: none;">
                    جاري تحميل الكاميرا...
                </div>

                <!-- Video Element -->
                <video id="cameraVideo" autoplay playsinline style="width: 100%; max-width: 640px; height: auto; border-radius: 8px; display: none;"></video>

                <!-- Canvas for captured image -->
                <canvas id="cameraCanvas" style="width: 100%; max-width: 640px; height: auto; border-radius: 8px; display: none;"></canvas>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" id="captureBtn" class="btn btn-primary" onclick="capturePhoto()" disabled>
                    <i class="fas fa-camera me-1"></i>
                    التقاط الصورة
                </button>
                <button type="button" id="retakeBtn" class="btn btn-warning" onclick="retakePhoto()" style="display: none;">
                    <i class="fas fa-redo me-1"></i>
                    إعادة التقاط
                </button>
                <button type="button" id="usePhotoBtn" class="btn btn-success" onclick="usePhoto()" disabled>
                    <i class="fas fa-check me-1"></i>
                    استخدام هذه الصورة
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Image Zoom Modal -->
<div class="modal fade" id="imageZoomModal" tabindex="-1" aria-labelledby="imageZoomModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageZoomModalLabel">
                    <i class="fas fa-search-plus me-2"></i>
                    عرض الصورة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0" style="overflow: hidden;">
                <div class="position-relative" style="height: 70vh; overflow: hidden;">
                    <img id="zoomedImage" src="" alt="صورة مكبرة"
                         style="max-width: 100%; max-height: 100%; object-fit: contain; transition: transform 0.2s; cursor: grab;" />
                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-outline-primary" onclick="zoomIn()">
                    <i class="fas fa-search-plus me-1"></i>
                    تكبير
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="zoomOut()">
                    <i class="fas fa-search-minus me-1"></i>
                    تصغير
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="resetZoom()">
                    <i class="fas fa-expand-arrows-alt me-1"></i>
                    إعادة تعيين
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <style>
        .photo-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 30px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .photo-upload-area:hover {
            border-color: #007bff;
            background-color: #e3f2fd;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,123,255,0.1);
        }

        .photo-placeholder {
            text-align: center;
        }

        .photo-preview {
            max-width: 100%;
            max-height: 200px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            object-fit: cover;
        }

        .photo-upload-area.has-image {
            border: 2px solid #28a745;
            background-color: #f8fff9;
            padding: 10px;
        }

        .photo-upload-area.has-image:hover {
            border-color: #20c997;
            background-color: #e8f5e8;
        }

        .camera-container {
            min-height: 260px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            border-radius: 10px;
            border: 2px dashed #dee2e6;
        }

        .camera-placeholder {
            text-align: center;
            padding: 30px;
        }
    </style>

    <style>
        .photo-preview-container {
            border: 2px dashed #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: border-color 0.3s ease;
        }

        .photo-preview-container:hover {
            border-color: #007bff;
        }

        .photo-preview-container img {
            transition: transform 0.3s ease;
        }

        .photo-preview-container img:hover {
            transform: scale(1.05);
        }

        .btn-outline-primary:hover,
        .btn-outline-success:hover,
        .btn-outline-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .card-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
        }

        .bg-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
        }

        #cameraVideo, #cameraCanvas {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .modal-content {
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn {
            transition: all 0.3s ease;
        }

        .alert {
            border-radius: 8px;
        }

        /* أنماط التابات */
        .nav-tabs .nav-link {
            border: none;
            border-radius: 0;
            color: #6c757d;
            font-weight: 500;
            padding: 15px 20px;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link:hover {
            border-color: transparent;
            color: #007bff;
            background-color: #f8f9fa;
        }

        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
            border-bottom: 2px solid #007bff;
        }

        .tab-content {
            padding-top: 20px;
        }

        .card-header-tabs {
            margin-bottom: 0;
        }

        .nav-tabs {
            border-bottom: 1px solid #dee2e6;
        }
    </style>

    <script>
        // تحديد الحد الأقصى لتاريخ الولادة (اليوم)
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.querySelector('input[type="date"]');
            if (dateInput) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.setAttribute('max', today);
            }

            // إعداد تحديث معاينة العنوان
            setupAddressPreview();

            // إعداد وظائف رفع الصورة
            setupPhotoUpload();

            // إعداد وظائف الكاميرا
            setupCamera();
        });

        function setupAddressPreview() {
            // إضافة مستمعين للأحداث لجميع حقول العنوان
            const addressFields = [
                '[name="Officer.ProvinceId"]',
                '[name="Officer.District"]',
                '[name="Officer.Subdistrict"]',
                '[name="Officer.Village"]',
                '[name="Officer.Neighborhood"]',
                '[name="Officer.Locality"]',
                '[name="Officer.Alley"]',
                '[name="Officer.HouseNumber"]',
                '[name="Officer.NearestLandmark"]'
            ];

            addressFields.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    element.addEventListener('input', updateAddressPreview);
                    element.addEventListener('change', updateAddressPreview);
                }
            });

            // تحديث أولي
            updateAddressPreview();
        }

        function updateAddressPreview() {
            const addressParts = [];

            // المحافظة
            const provinceSelect = document.querySelector('[name="Officer.ProvinceId"]');
            if (provinceSelect && provinceSelect.value) {
                const selectedOption = provinceSelect.options[provinceSelect.selectedIndex];
                if (selectedOption && selectedOption.text !== 'اختر المحافظة') {
                    addressParts.push(`محافظة ${selectedOption.text}`);
                }
            }

            // القضاء
            const district = document.querySelector('[name="Officer.District"]')?.value?.trim();
            if (district) {
                addressParts.push(`قضاء ${district}`);
            }

            // الناحية
            const subdistrict = document.querySelector('[name="Officer.Subdistrict"]')?.value?.trim();
            if (subdistrict) {
                addressParts.push(`ناحية ${subdistrict}`);
            }

            // القرية
            const village = document.querySelector('[name="Officer.Village"]')?.value?.trim();
            if (village) {
                addressParts.push(`قرية ${village}`);
            }

            // الحي
            const neighborhood = document.querySelector('[name="Officer.Neighborhood"]')?.value?.trim();
            if (neighborhood) {
                addressParts.push(`حي ${neighborhood}`);
            }

            // المحلة
            const locality = document.querySelector('[name="Officer.Locality"]')?.value?.trim();
            if (locality) {
                addressParts.push(`محلة ${locality}`);
            }

            // الزقاق
            const alley = document.querySelector('[name="Officer.Alley"]')?.value?.trim();
            if (alley) {
                addressParts.push(`زقاق ${alley}`);
            }

            // رقم الدار
            const houseNumber = document.querySelector('[name="Officer.HouseNumber"]')?.value?.trim();
            if (houseNumber) {
                addressParts.push(`دار رقم ${houseNumber}`);
            }

            // أقرب نقطة دالة
            const landmark = document.querySelector('[name="Officer.NearestLandmark"]')?.value?.trim();
            if (landmark) {
                addressParts.push(`قرب ${landmark}`);
            }

            const addressPreview = document.getElementById('addressPreview');
            if (addressPreview) {
                if (addressParts.length > 0) {
                    addressPreview.textContent = addressParts.join(' - ');
                    addressPreview.className = 'mt-2 text-primary fw-bold';
                } else {
                    addressPreview.textContent = 'سيظهر العنوان هنا عند ملء البيانات...';
                    addressPreview.className = 'mt-2 text-muted';
                }
            }
        }

        // Photo upload functionality
        function setupPhotoUpload() {
            const photoFile = document.getElementById('photoFile');
            const photoDisplayArea = document.getElementById('photoDisplayArea');
            const photoPlaceholder = document.getElementById('photoPlaceholder');
            const previewImage = document.getElementById('previewImage');
            const photoActions = document.getElementById('photoActions');
            const removePhoto = document.getElementById('removePhoto');

            // Event listeners
            photoFile.addEventListener('change', handleFileUpload);
            removePhoto.addEventListener('click', clearPhoto);

            function handleFileUpload(event) {
                const file = event.target.files[0];
                if (!file) return;

                // Validate file type
                if (!file.type.startsWith('image/')) {
                    alert('يرجى اختيار ملف صورة صحيح');
                    photoFile.value = '';
                    return;
                }

                // Validate file size (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
                    photoFile.value = '';
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    showPhotoPreview(e.target.result);
                };
                reader.readAsDataURL(file);
            }

            function showPhotoPreview(dataUrl) {
                previewImage.src = dataUrl;
                previewImage.style.display = 'block';
                photoPlaceholder.style.display = 'none';
                photoActions.style.display = 'block';
                photoDisplayArea.classList.add('has-image');
            }

            function clearPhoto() {
                previewImage.style.display = 'none';
                previewImage.src = '';
                photoPlaceholder.style.display = 'block';
                photoActions.style.display = 'none';
                photoDisplayArea.classList.remove('has-image');
                photoFile.value = '';

                // Remove hidden input for camera data
                const hiddenInput = document.getElementById('photoDataInput');
                if (hiddenInput) {
                    hiddenInput.remove();
                }
            }
        }

        // Global function to select photo
        function selectPhoto() {
            document.getElementById('photoFile').click();
        }

        // متغيرات عامة للكاميرا
        let currentStream = null;
        let availableCameras = [];
        let currentZoom = 1;
        let capturedImageData = null;

        // دالة اختيار صورة من ملف
        function selectFromFile() {
            console.log('تم النقر على اختيار صورة من ملف');
            const fileInput = document.getElementById('photoFile');
            if (fileInput) {
                fileInput.click();
            } else {
                console.error('لم يتم العثور على عنصر اختيار الملف');
            }
        }

        // Global function to open camera
        function openCamera() {
            const cameraModal = new bootstrap.Modal(document.getElementById('cameraModal'));
            cameraModal.show();
            getCameras();
        }

        async function getCameras() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                availableCameras = devices.filter(device => device.kind === 'videoinput');

                const cameraSelect = document.getElementById('cameraSelect');
                cameraSelect.innerHTML = '<option value="">اختر كاميرا</option>';

                availableCameras.forEach((camera, index) => {
                    const option = document.createElement('option');
                    option.value = camera.deviceId;
                    option.textContent = camera.label || `كاميرا ${index + 1}`;
                    cameraSelect.appendChild(option);
                });

                if (availableCameras.length === 0) {
                    cameraSelect.innerHTML = '<option value="">لا توجد كاميرات متاحة</option>';
                }
            } catch (error) {
                console.error('خطأ في الحصول على الكاميرات:', error);
            }
        }

        async function switchCamera() {
            const cameraSelect = document.getElementById('cameraSelect');
            const selectedDeviceId = cameraSelect.value;

            if (!selectedDeviceId) return;

            try {
                // إيقاف الكاميرا الحالية إن وجدت
                if (currentStream) {
                    currentStream.getTracks().forEach(track => track.stop());
                }

                const constraints = {
                    video: {
                        deviceId: { exact: selectedDeviceId },
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                };

                const cameraStatus = document.getElementById('cameraStatus');
                cameraStatus.style.display = 'block';
                cameraStatus.textContent = 'جاري تحميل الكاميرا...';

                currentStream = await navigator.mediaDevices.getUserMedia(constraints);

                const video = document.getElementById('cameraVideo');
                video.srcObject = currentStream;
                video.style.display = 'block';

                cameraStatus.style.display = 'none';
                document.getElementById('captureBtn').disabled = false;

            } catch (error) {
                console.error('خطأ في تشغيل الكاميرا:', error);
                const cameraStatus = document.getElementById('cameraStatus');
                cameraStatus.style.display = 'block';
                cameraStatus.className = 'alert alert-danger';
                cameraStatus.textContent = 'فشل في تشغيل الكاميرا. يرجى التأكد من الأذونات.';
            }
        }

        function capturePhoto() {
            const video = document.getElementById('cameraVideo');
            const canvas = document.getElementById('cameraCanvas');
            const context = canvas.getContext('2d');

            // تعيين حجم الكانفاس ليطابق الفيديو
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // رسم الإطار الحالي على الكانفاس
            context.drawImage(video, 0, 0);

            // الحصول على بيانات الصورة
            capturedImageData = canvas.toDataURL('image/jpeg', 0.8);

            // عرض الصورة الملتقطة
            video.style.display = 'none';
            canvas.style.display = 'block';

            // تحديث واجهة المستخدم
            document.getElementById('captureBtn').disabled = true;
            document.getElementById('retakeBtn').style.display = 'inline-block';
            document.getElementById('usePhotoBtn').disabled = false;
        }

        function retakePhoto() {
            const video = document.getElementById('cameraVideo');
            const canvas = document.getElementById('cameraCanvas');

            // عرض معاينة الكاميرا مرة أخرى
            video.style.display = 'block';
            canvas.style.display = 'none';

            // إعادة تعيين البيانات الملتقطة
            capturedImageData = null;

            // تحديث واجهة المستخدم
            document.getElementById('captureBtn').disabled = false;
            document.getElementById('retakeBtn').style.display = 'none';
            document.getElementById('usePhotoBtn').disabled = true;
        }

        function usePhoto() {
            if (capturedImageData) {
                // عرض الصورة الملتقطة في المعاينة الرئيسية
                const previewImage = document.getElementById('previewImage');
                const photoPlaceholder = document.getElementById('photoPlaceholder');
                const photoActions = document.getElementById('photoActions');
                const photoDisplayArea = document.getElementById('photoDisplayArea');

                previewImage.src = capturedImageData;
                previewImage.style.display = 'block';
                photoPlaceholder.style.display = 'none';
                photoActions.style.display = 'block';
                photoDisplayArea.classList.add('has-image');

                // إنشاء حقل مخفي مع بيانات الصورة
                let hiddenInput = document.getElementById('photoDataInput');
                if (!hiddenInput) {
                    hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.id = 'photoDataInput';
                    hiddenInput.name = 'PhotoDataUrl';
                    document.querySelector('form').appendChild(hiddenInput);
                }
                hiddenInput.value = capturedImageData;

                // إغلاق النافذة المنبثقة
                const cameraModal = bootstrap.Modal.getInstance(document.getElementById('cameraModal'));
                if (cameraModal) {
                    cameraModal.hide();
                }
            }
        }

        // تحديث دالة setupCamera لتعمل مع النظام الجديد
        function setupCamera() {
            // لا حاجة لإعداد معقد هنا لأن الدوال معرفة بالفعل
        }

        // دوال تكبير الصورة
        function zoomImage(imageSrc) {
            const zoomedImage = document.getElementById('zoomedImage');
            zoomedImage.src = imageSrc;
            currentZoom = 1;
            zoomedImage.style.transform = 'scale(1)';

            const imageZoomModal = new bootstrap.Modal(document.getElementById('imageZoomModal'));
            imageZoomModal.show();
        }

        function zoomIn() {
            currentZoom += 0.2;
            document.getElementById('zoomedImage').style.transform = `scale(${currentZoom})`;
        }

        function zoomOut() {
            if (currentZoom > 0.4) {
                currentZoom -= 0.2;
                document.getElementById('zoomedImage').style.transform = `scale(${currentZoom})`;
            }
        }

        function resetZoom() {
            currentZoom = 1;
            document.getElementById('zoomedImage').style.transform = 'scale(1)';
        }

        // دوال الكومبوبوكس المترابط
        async function loadDirectorates() {
            const agencyId = document.getElementById('agencySelect').value;
            const directorateSelect = document.getElementById('directorateSelect');
            const departmentSelect = document.getElementById('departmentSelect');
            const divisionSelect = document.getElementById('divisionSelect');

            // إعادة تعيين القوائم التابعة
            directorateSelect.innerHTML = '<option value="">جاري التحميل...</option>';
            departmentSelect.innerHTML = '<option value="">اختر القسم أولاً</option>';
            divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';

            departmentSelect.disabled = true;
            divisionSelect.disabled = true;

            if (!agencyId) {
                directorateSelect.innerHTML = '<option value="">اختر المديرية أولاً</option>';
                directorateSelect.disabled = true;
                updateJobStructurePreview();
                return;
            }

            try {
                const response = await fetch(`/Officers/Create?handler=Directorates&agencyId=${agencyId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const directorates = await response.json();

                directorateSelect.innerHTML = '<option value="">اختر المديرية</option>';
                directorates.forEach(directorate => {
                    directorateSelect.innerHTML += `<option value="${directorate.id}">${directorate.name}</option>`;
                });

                directorateSelect.disabled = false;
                updateJobStructurePreview();
            } catch (error) {
                console.error('خطأ في تحميل المديريات:', error);
                directorateSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
            }
        }

        async function loadDepartments() {
            const directorateId = document.getElementById('directorateSelect').value;
            const departmentSelect = document.getElementById('departmentSelect');
            const divisionSelect = document.getElementById('divisionSelect');

            // إعادة تعيين القوائم التابعة
            departmentSelect.innerHTML = '<option value="">جاري التحميل...</option>';
            divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';
            divisionSelect.disabled = true;

            if (!directorateId) {
                departmentSelect.innerHTML = '<option value="">اختر القسم أولاً</option>';
                departmentSelect.disabled = true;
                updateJobStructurePreview();
                return;
            }

            try {
                const response = await fetch(`/Officers/Create?handler=Departments&directorateId=${directorateId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const departments = await response.json();

                departmentSelect.innerHTML = '<option value="">اختر القسم</option>';
                departments.forEach(department => {
                    departmentSelect.innerHTML += `<option value="${department.id}">${department.name}</option>`;
                });

                departmentSelect.disabled = false;
                updateJobStructurePreview();
            } catch (error) {
                console.error('خطأ في تحميل الأقسام:', error);
                departmentSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
            }
        }

        async function loadDivisions() {
            const departmentId = document.getElementById('departmentSelect').value;
            const divisionSelect = document.getElementById('divisionSelect');

            divisionSelect.innerHTML = '<option value="">جاري التحميل...</option>';

            if (!departmentId) {
                divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';
                divisionSelect.disabled = true;
                updateJobStructurePreview();
                return;
            }

            try {
                const response = await fetch(`/Officers/Create?handler=Divisions&departmentId=${departmentId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const divisions = await response.json();

                divisionSelect.innerHTML = '<option value="">اختر الشعبة</option>';
                divisions.forEach(division => {
                    divisionSelect.innerHTML += `<option value="${division.id}">${division.name}</option>`;
                });

                divisionSelect.disabled = false;
                updateJobStructurePreview();
            } catch (error) {
                console.error('خطأ في تحميل الشعب:', error);
                divisionSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
            }
        }

        function updateJobStructurePreview() {
            const agencySelect = document.getElementById('agencySelect');
            const directorateSelect = document.getElementById('directorateSelect');
            const departmentSelect = document.getElementById('departmentSelect');
            const divisionSelect = document.getElementById('divisionSelect');
            const preview = document.getElementById('jobStructurePreview');

            let structure = [];

            if (agencySelect.value) {
                const agencyText = agencySelect.options[agencySelect.selectedIndex].text;
                structure.push(`<i class="fas fa-building text-primary"></i> ${agencyText}`);
            }

            if (directorateSelect.value) {
                const directorateText = directorateSelect.options[directorateSelect.selectedIndex].text;
                structure.push(`<i class="fas fa-sitemap text-info"></i> ${directorateText}`);
            }

            if (departmentSelect.value) {
                const departmentText = departmentSelect.options[departmentSelect.selectedIndex].text;
                structure.push(`<i class="fas fa-users text-warning"></i> ${departmentText}`);
            }

            if (divisionSelect.value) {
                const divisionText = divisionSelect.options[divisionSelect.selectedIndex].text;
                structure.push(`<i class="fas fa-user-tie text-success"></i> ${divisionText}`);
            }

            if (structure.length === 0) {
                preview.innerHTML = '<i class="fas fa-info-circle me-2"></i>يرجى اختيار الوكالة لبدء التنقل في الهيكل الوظيفي';
            } else {
                preview.innerHTML = structure.join(' <i class="fas fa-arrow-left mx-2 text-muted"></i> ');
            }
        }

        // إضافة مستمعين لتحديث المعاينة
        document.addEventListener('DOMContentLoaded', function() {
            const divisionSelect = document.getElementById('divisionSelect');
            if (divisionSelect) {
                divisionSelect.addEventListener('change', updateJobStructurePreview);
            }
        });

    </script>
}
