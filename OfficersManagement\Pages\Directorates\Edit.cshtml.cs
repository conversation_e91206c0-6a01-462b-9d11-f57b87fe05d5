using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Directorates
{
    public class EditModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public EditModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Directorate Directorate { get; set; } = default!;

        public SelectList AgenciesList { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var directorate = await _context.Directorates.FirstOrDefaultAsync(m => m.Id == id);
            if (directorate == null)
            {
                return NotFound();
            }
            Directorate = directorate;
            await LoadAgenciesAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadAgenciesAsync();
                return Page();
            }

            try
            {
                _context.Attach(Directorate).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"تم تعديل المديرية '{Directorate.Name}' بنجاح";
                return RedirectToPage("./Index");
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!DirectorateExists(Directorate.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "حدث خطأ أثناء حفظ البيانات: " + ex.Message);
                await LoadAgenciesAsync();
                return Page();
            }
        }

        private bool DirectorateExists(int id)
        {
            return _context.Directorates.Any(e => e.Id == id);
        }

        private async Task LoadAgenciesAsync()
        {
            var agencies = await _context.Agencies
                .Where(a => a.IsActive)
                .OrderBy(a => a.Name)
                .ToListAsync();

            AgenciesList = new SelectList(agencies, "Id", "Name");
        }
    }
}
