using System.ComponentModel.DataAnnotations;

namespace OfficersManagement.Models
{
    public class Directorate
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المديرية مطلوب")]
        [Display(Name = "اسم المديرية")]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        [StringLength(500)]
        public string? Description { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Required]
        [Display(Name = "الوكالة")]
        public int AgencyId { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Agency? Agency { get; set; }
        public virtual ICollection<Department> Departments { get; set; } = new List<Department>();
    }
}
