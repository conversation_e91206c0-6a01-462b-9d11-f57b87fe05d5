﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OfficersManagement.Migrations
{
    /// <inheritdoc />
    public partial class UpdateOfficerModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "LastN<PERSON>",
                table: "Officers",
                newName: "GrandfatherN<PERSON>");

            migrationBuilder.CreateIndex(
                name: "IX_Officers_FullName",
                table: "Officers",
                columns: new[] { "FirstName", "SecondName", "ThirdName", "GrandfatherName" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Officers_FullName",
                table: "Officers");

            migrationBuilder.RenameColumn(
                name: "GrandfatherN<PERSON>",
                table: "Officers",
                newName: "LastName");
        }
    }
}
