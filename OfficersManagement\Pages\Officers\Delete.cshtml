@page
@model OfficersManagement.Pages.Officers.DeleteModel
@{
    ViewData["Title"] = "حذف الضابط";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-times me-2"></i>
                        حذف الضابط
                    </h4>
                </div>
                <div class="card-body">
                    @if (Model.Officer == null)
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الضابط غير موجود
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تحذير:</strong> هل أنت متأكد من حذف بيانات هذا الضابط؟ هذا الإجراء لا يمكن التراجع عنه.
                        </div>

                        <div class="row">
                            <!-- المعلومات الأساسية -->
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">المعلومات الأساسية</h6>
                                    </div>
                                    <div class="card-body">
                                        <dl class="row">
                                            <dt class="col-sm-5">رقم الموظف:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge bg-secondary">@Model.Officer.EmployeeNumber</span>
                                            </dd>

                                            <dt class="col-sm-5">الرقم الإحصائي:</dt>
                                            <dd class="col-sm-7">@Model.Officer.StatisticalNumber</dd>

                                            <dt class="col-sm-5">الرتبة:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge bg-success">@(Model.Officer.Rank?.Name ?? "غير محدد")</span>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>

                            <!-- المعلومات الشخصية -->
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">المعلومات الشخصية</h6>
                                    </div>
                                    <div class="card-body">
                                        <dl class="row">
                                            <dt class="col-sm-5">الاسم الكامل:</dt>
                                            <dd class="col-sm-7">
                                                <strong>@Model.Officer.FullName</strong>
                                            </dd>

                                            <dt class="col-sm-5">محل الولادة:</dt>
                                            <dd class="col-sm-7">@Model.Officer.PlaceOfBirth</dd>

                                            <dt class="col-sm-5">تاريخ الولادة:</dt>
                                            <dd class="col-sm-7">@Model.Officer.DateOfBirth.ToString("yyyy/MM/dd")</dd>

                                            <dt class="col-sm-5">العمر:</dt>
                                            <dd class="col-sm-7">
                                                <span class="text-info">@Model.Officer.Age</span>
                                            </dd>

                                            <dt class="col-sm-5">الحالة الاجتماعية:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge bg-primary">@Model.Officer.MaritalStatus</span>
                                            </dd>

                                            <dt class="col-sm-5">فصيلة الدم:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge bg-danger">@Model.Officer.BloodType</span>
                                            </dd>

                                            <dt class="col-sm-5">الحالة الصحية:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge @(Model.Officer.HealthStatus == "سليم" ? "bg-success" : Model.Officer.HealthStatus == "مريض" ? "bg-warning" : "bg-secondary")">@Model.Officer.HealthStatus</span>
                                            </dd>

                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- تفاصيل الاسم -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">تفاصيل الاسم</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <dt>اللقب:</dt>
                                                <dd>@Model.Officer.Title</dd>
                                            </div>
                                            <div class="col-md-3">
                                                <dt>الاسم الأول:</dt>
                                                <dd>@Model.Officer.FirstName</dd>
                                            </div>
                                            <div class="col-md-3">
                                                <dt>الاسم الثاني:</dt>
                                                <dd>@Model.Officer.SecondName</dd>
                                            </div>
                                            <div class="col-md-2">
                                                <dt>الاسم الثالث:</dt>
                                                <dd>@Model.Officer.ThirdName</dd>
                                            </div>
                                            <div class="col-md-2">
                                                <dt>الاسم الرابع:</dt>
                                                <dd>@Model.Officer.FourthName</dd>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <form method="post">
                            <input type="hidden" asp-for="Officer.Id" />
                            
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <a asp-page="./Index" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-1"></i>
                                            العودة للقائمة
                                        </a>
                                        <div>
                                            <a asp-page="./Details" asp-route-id="@Model.Officer.Id" class="btn btn-info me-2">
                                                <i class="fas fa-eye me-1"></i>
                                                عرض التفاصيل
                                            </a>
                                            <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الضابط؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                <i class="fas fa-trash me-1"></i>
                                                تأكيد الحذف
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
