@page
@model OfficersManagement.Pages.Divisions.DeleteModel
@{
    ViewData["Title"] = "حذف الشعبة";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-danger">
                    <i class="fas fa-trash me-2"></i>
                    حذف الشعبة
                </h2>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف الشعبة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هل أنت متأكد من حذف هذه الشعبة؟ هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات الشعبة:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الرقم:</strong></td>
                                    <td>@Model.Division.Id</td>
                                </tr>
                                <tr>
                                    <td><strong>الاسم:</strong></td>
                                    <td>@Model.Division.Name</td>
                                </tr>
                                <tr>
                                    <td><strong>القسم:</strong></td>
                                    <td><span class="badge bg-warning">@Model.Division.Department?.Name</span></td>
                                </tr>
                                <tr>
                                    <td><strong>المديرية:</strong></td>
                                    <td><span class="badge bg-info">@Model.Division.Department?.Directorate?.Name</span></td>
                                </tr>
                                <tr>
                                    <td><strong>الوكالة:</strong></td>
                                    <td><span class="badge bg-primary">@Model.Division.Department?.Directorate?.Agency?.Name</span></td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        @if (Model.Division.IsActive)
                                        {
                                            <span class="badge bg-success">نشط</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">غير نشط</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <form method="post" class="mt-4">
                        <input type="hidden" asp-for="Division.Id" />
                        <div class="d-flex justify-content-end gap-2">
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>
                                تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
