using System.ComponentModel.DataAnnotations;

namespace OfficersManagement.Models
{
    public class Agency
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الوكالة مطلوب")]
        [Display(Name = "اسم الوكالة")]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        [StringLength(500)]
        public string? Description { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<Directorate> Directorates { get; set; } = new List<Directorate>();
    }
}
