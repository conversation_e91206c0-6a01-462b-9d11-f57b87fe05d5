﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using OfficersManagement.Data;

#nullable disable

namespace OfficersManagement.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250621122813_AddLocationFieldsToOfficer")]
    partial class AddLocationFieldsToOfficer
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("OfficersManagement.Models.District", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("ProvinceId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ProvinceId");

                    b.ToTable("Districts");
                });

            modelBuilder.Entity("OfficersManagement.Models.Officer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Alley")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("BloodType")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("int");

                    b.Property<string>("EmployeeNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FourthName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("HealthStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("HouseNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Locality")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MaritalStatus")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("NearestLandmark")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Neighborhood")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PhotoContentType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<byte[]>("PhotoData")
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("PlaceOfBirth")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("ProvinceId")
                        .HasColumnType("int");

                    b.Property<int>("RankId")
                        .HasColumnType("int");

                    b.Property<string>("SecondName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("StatisticalNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int?>("SubdistrictId")
                        .HasColumnType("int");

                    b.Property<string>("ThirdName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("VillageId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DistrictId");

                    b.HasIndex("EmployeeNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Officers_EmployeeNumber");

                    b.HasIndex("ProvinceId");

                    b.HasIndex("RankId");

                    b.HasIndex("StatisticalNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Officers_StatisticalNumber");

                    b.HasIndex("SubdistrictId");

                    b.HasIndex("VillageId");

                    b.HasIndex("FirstName", "FourthName")
                        .HasDatabaseName("IX_Officers_Name");

                    b.HasIndex("FirstName", "SecondName", "ThirdName", "FourthName")
                        .IsUnique()
                        .HasDatabaseName("IX_Officers_FullName");

                    b.ToTable("Officers");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            BloodType = "",
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            DateOfBirth = new DateTime(1980, 5, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EmployeeNumber = "EMP001",
                            FirstName = "أحمد",
                            FourthName = "الأحمد",
                            HealthStatus = "",
                            MaritalStatus = "",
                            PlaceOfBirth = "بغداد",
                            RankId = 5,
                            SecondName = "محمد",
                            StatisticalNumber = "STAT001",
                            ThirdName = "علي",
                            Title = "الدكتور",
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 2,
                            BloodType = "",
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            DateOfBirth = new DateTime(1985, 8, 22, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EmployeeNumber = "EMP002",
                            FirstName = "فاطمة",
                            FourthName = "الزهراء",
                            HealthStatus = "",
                            MaritalStatus = "",
                            PlaceOfBirth = "البصرة",
                            RankId = 6,
                            SecondName = "حسن",
                            StatisticalNumber = "STAT002",
                            ThirdName = "عبدالله",
                            Title = "المهندسة",
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 3,
                            BloodType = "",
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            DateOfBirth = new DateTime(1990, 12, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EmployeeNumber = "EMP003",
                            FirstName = "خالد",
                            FourthName = "المحمود",
                            HealthStatus = "",
                            MaritalStatus = "",
                            PlaceOfBirth = "الموصل",
                            RankId = 7,
                            SecondName = "عبدالرحمن",
                            StatisticalNumber = "STAT003",
                            ThirdName = "صالح",
                            Title = "الأستاذ",
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });

            modelBuilder.Entity("OfficersManagement.Models.Province", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Provinces");
                });

            modelBuilder.Entity("OfficersManagement.Models.Rank", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("IX_Ranks_Name");

                    b.HasIndex("Order")
                        .HasDatabaseName("IX_Ranks_Order");

                    b.ToTable("Ranks");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "أعلى رتبة عسكرية",
                            IsActive = true,
                            Name = "فريق أول",
                            Order = 1,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة عسكرية عليا",
                            IsActive = true,
                            Name = "فريق",
                            Order = 2,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 3,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة عسكرية عليا",
                            IsActive = true,
                            Name = "لواء",
                            Order = 3,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 4,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة عسكرية عليا",
                            IsActive = true,
                            Name = "عميد",
                            Order = 4,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 5,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة ضباط كبار",
                            IsActive = true,
                            Name = "عقيد",
                            Order = 5,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 6,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة ضباط كبار",
                            IsActive = true,
                            Name = "مقدم",
                            Order = 6,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 7,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة ضباط",
                            IsActive = true,
                            Name = "رائد",
                            Order = 7,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 8,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة ضباط",
                            IsActive = true,
                            Name = "نقيب",
                            Order = 8,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 9,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة ضباط صغار",
                            IsActive = true,
                            Name = "ملازم أول",
                            Order = 9,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 10,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة ضباط صغار",
                            IsActive = true,
                            Name = "ملازم",
                            Order = 10,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 11,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة ضباط صف",
                            IsActive = true,
                            Name = "رقيب أول",
                            Order = 11,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 12,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة ضباط صف",
                            IsActive = true,
                            Name = "رقيب",
                            Order = 12,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 13,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة أفراد",
                            IsActive = true,
                            Name = "عريف",
                            Order = 13,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 14,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "رتبة أفراد",
                            IsActive = true,
                            Name = "جندي أول",
                            Order = 14,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 15,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "أدنى رتبة عسكرية",
                            IsActive = true,
                            Name = "جندي",
                            Order = 15,
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });

            modelBuilder.Entity("OfficersManagement.Models.Subdistrict", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("DistrictId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("DistrictId");

                    b.ToTable("Subdistricts");
                });

            modelBuilder.Entity("OfficersManagement.Models.Village", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("SubdistrictId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("SubdistrictId");

                    b.ToTable("Villages");
                });

            modelBuilder.Entity("OfficersManagement.Models.District", b =>
                {
                    b.HasOne("OfficersManagement.Models.Province", "Province")
                        .WithMany()
                        .HasForeignKey("ProvinceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Province");
                });

            modelBuilder.Entity("OfficersManagement.Models.Officer", b =>
                {
                    b.HasOne("OfficersManagement.Models.District", "District")
                        .WithMany("Officers")
                        .HasForeignKey("DistrictId");

                    b.HasOne("OfficersManagement.Models.Province", "Province")
                        .WithMany("Officers")
                        .HasForeignKey("ProvinceId");

                    b.HasOne("OfficersManagement.Models.Rank", "Rank")
                        .WithMany("Officers")
                        .HasForeignKey("RankId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("OfficersManagement.Models.Subdistrict", "Subdistrict")
                        .WithMany("Officers")
                        .HasForeignKey("SubdistrictId");

                    b.HasOne("OfficersManagement.Models.Village", "Village")
                        .WithMany("Officers")
                        .HasForeignKey("VillageId");

                    b.Navigation("District");

                    b.Navigation("Province");

                    b.Navigation("Rank");

                    b.Navigation("Subdistrict");

                    b.Navigation("Village");
                });

            modelBuilder.Entity("OfficersManagement.Models.Subdistrict", b =>
                {
                    b.HasOne("OfficersManagement.Models.District", "District")
                        .WithMany("Subdistricts")
                        .HasForeignKey("DistrictId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("District");
                });

            modelBuilder.Entity("OfficersManagement.Models.Village", b =>
                {
                    b.HasOne("OfficersManagement.Models.Subdistrict", "Subdistrict")
                        .WithMany("Villages")
                        .HasForeignKey("SubdistrictId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subdistrict");
                });

            modelBuilder.Entity("OfficersManagement.Models.District", b =>
                {
                    b.Navigation("Officers");

                    b.Navigation("Subdistricts");
                });

            modelBuilder.Entity("OfficersManagement.Models.Province", b =>
                {
                    b.Navigation("Officers");
                });

            modelBuilder.Entity("OfficersManagement.Models.Rank", b =>
                {
                    b.Navigation("Officers");
                });

            modelBuilder.Entity("OfficersManagement.Models.Subdistrict", b =>
                {
                    b.Navigation("Officers");

                    b.Navigation("Villages");
                });

            modelBuilder.Entity("OfficersManagement.Models.Village", b =>
                {
                    b.Navigation("Officers");
                });
#pragma warning restore 612, 618
        }
    }
}
