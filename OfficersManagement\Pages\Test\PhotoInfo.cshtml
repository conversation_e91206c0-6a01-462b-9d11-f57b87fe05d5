@page "/test/photo-info"
@model OfficersManagement.Pages.Test.PhotoInfoModel
@{
    ViewData["Title"] = "معلومات الصور في قاعدة البيانات";
}

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-database me-2"></i>
                        معلومات الصور المحفوظة في قاعدة البيانات
                    </h4>
                </div>
                <div class="card-body">
                    @if (Model.Officers.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>رقم الضابط</th>
                                        <th>الاسم</th>
                                        <th>حالة الصورة</th>
                                        <th>حجم البيانات (بايت)</th>
                                        <th>نوع المحتوى</th>
                                        <th>معاينة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var officer in Model.Officers)
                                    {
                                        <tr>
                                            <td>@officer.Id</td>
                                            <td>@officer.FullName</td>
                                            <td>
                                                @if (officer.PhotoData != null && officer.PhotoData.Length > 0)
                                                {
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>
                                                        محفوظة
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-times me-1"></i>
                                                        غير موجودة
                                                    </span>
                                                }
                                            </td>
                                            <td>
                                                @if (officer.PhotoData != null && officer.PhotoData.Length > 0)
                                                {
                                                    <span class="text-info">
                                                        @string.Format("{0:N0}", officer.PhotoData.Length) بايت
                                                        (@string.Format("{0:F2}", officer.PhotoData.Length / 1024.0) كيلوبايت)
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(officer.PhotoContentType))
                                                {
                                                    <span class="badge bg-info">@officer.PhotoContentType</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (officer.PhotoData != null && officer.PhotoData.Length > 0)
                                                {
                                                    <img src="/api/photo/@officer.Id" alt="صورة @officer.FullName" 
                                                         style="width: 50px; height: 50px; object-fit: fill; border-radius: 4px; border: 1px solid #ddd;" />
                                                }
                                                else
                                                {
                                                    <div style="width: 50px; height: 50px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                                        <i class="fas fa-user text-muted"></i>
                                                    </div>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle me-2"></i>ملخص:</h5>
                                <ul class="mb-0">
                                    <li><strong>إجمالي الضباط:</strong> @Model.Officers.Count()</li>
                                    <li><strong>الضباط مع صور:</strong> @Model.Officers.Count(o => o.PhotoData != null && o.PhotoData.Length > 0)</li>
                                    <li><strong>الضباط بدون صور:</strong> @Model.Officers.Count(o => o.PhotoData == null || o.PhotoData.Length == 0)</li>
                                    <li><strong>إجمالي حجم الصور:</strong> @string.Format("{0:F2}", Model.Officers.Where(o => o.PhotoData != null).Sum(o => o.PhotoData.Length) / 1024.0 / 1024.0) ميجابايت</li>
                                </ul>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لا توجد بيانات ضباط في قاعدة البيانات.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
