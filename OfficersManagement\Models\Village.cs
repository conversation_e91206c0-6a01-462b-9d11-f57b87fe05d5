using System.ComponentModel.DataAnnotations;

namespace OfficersManagement.Models
{
    public class Village
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم القرية مطلوب")]
        [Display(Name = "اسم القرية")]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "الناحية مطلوبة")]
        [Display(Name = "الناحية")]
        public int SubdistrictId { get; set; }

        [Display(Name = "الوصف")]
        [StringLength(200)]
        public string? Description { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Subdistrict Subdistrict { get; set; } = null!;
        public virtual ICollection<Officer> Officers { get; set; } = new List<Officer>();
    }
}
