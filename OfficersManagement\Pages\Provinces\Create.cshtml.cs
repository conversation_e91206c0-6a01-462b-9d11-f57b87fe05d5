using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Provinces
{
    public class CreateModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public CreateModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Province Province { get; set; } = default!;

        public IActionResult OnGet()
        {
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            // التحقق من عدم تكرار اسم المحافظة
            var existingProvince = await _context.Provinces
                .FirstOrDefaultAsync(p => p.Name.ToLower() == Province.Name.ToLower());

            if (existingProvince != null)
            {
                ModelState.AddModelError("Province.Name", "اسم المحافظة موجود مسبقاً");
                return Page();
            }

            Province.CreatedAt = DateTime.Now;
            Province.UpdatedAt = DateTime.Now;

            _context.Provinces.Add(Province);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "تم إضافة المحافظة بنجاح";
            return RedirectToPage("./Index");
        }
    }
}
