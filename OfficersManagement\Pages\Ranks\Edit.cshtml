@page
@model OfficersManagement.Pages.Ranks.EditModel
@{
    ViewData["Title"] = "تعديل الرتبة";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الرتبة
                    </h4>
                </div>
                <div class="card-body">
                    @if (Model.Rank == null)
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الرتبة غير موجودة
                        </div>
                    }
                    else
                    {
                        <form method="post">
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                            
                            <input type="hidden" asp-for="Rank.Id" />
                            <input type="hidden" asp-for="Rank.CreatedAt" />
                            
                            <div class="row">
                                <!-- اسم الرتبة -->
                                <div class="col-12 mb-3">
                                    <label asp-for="Rank.Name" class="form-label"></label>
                                    <input asp-for="Rank.Name" class="form-control" placeholder="مثال: عقيد" />
                                    <span asp-validation-for="Rank.Name" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="row">
                                <!-- ترتيب الرتبة -->
                                <div class="col-md-6 mb-3">
                                    <label asp-for="Rank.Order" class="form-label"></label>
                                    <input asp-for="Rank.Order" class="form-control" type="number" min="1" max="100" />
                                    <div class="form-text">الرقم الأصغر يعني رتبة أعلى</div>
                                    <span asp-validation-for="Rank.Order" class="text-danger"></span>
                                </div>

                                <!-- الحالة -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الحالة</label>
                                    <div class="form-check form-switch">
                                        <input asp-for="Rank.IsActive" class="form-check-input" type="checkbox" />
                                        <label asp-for="Rank.IsActive" class="form-check-label">
                                            نشط
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- الوصف -->
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label asp-for="Rank.Description" class="form-label"></label>
                                    <textarea asp-for="Rank.Description" class="form-control" rows="3" 
                                              placeholder="وصف اختياري للرتبة..."></textarea>
                                    <span asp-validation-for="Rank.Description" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- معلومات إضافية -->
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">معلومات إضافية</h6>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <strong>عدد الضباط:</strong><br>
                                                    <span class="badge bg-info">@Model.OfficersCount</span>
                                                </div>
                                                <div class="col-md-4">
                                                    <strong>تاريخ الإنشاء:</strong><br>
                                                    <span class="text-muted">@Model.Rank.CreatedAt.ToString("yyyy/MM/dd")</span>
                                                </div>
                                                <div class="col-md-4">
                                                    <strong>الترتيب الحالي:</strong><br>
                                                    <span class="badge bg-primary">@Model.Rank.Order</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تحذيرات -->
                            @if (Model.OfficersCount > 0)
                            {
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>تنبيه:</strong> هذه الرتبة مرتبطة بـ @Model.OfficersCount ضابط. 
                                            تغيير اسم الرتبة سيؤثر على جميع الضباط المرتبطين بها.
                                        </div>
                                    </div>
                                </div>
                            }

                            <!-- أزرار الإجراءات -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <a asp-page="./Index" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-1"></i>
                                            العودة للقائمة
                                        </a>
                                        <div>
                                            <a asp-page="./Details" asp-route-id="@Model.Rank.Id" class="btn btn-info me-2">
                                                <i class="fas fa-eye me-1"></i>
                                                عرض التفاصيل
                                            </a>
                                            <button type="submit" class="btn btn-warning">
                                                <i class="fas fa-save me-1"></i>
                                                حفظ التغييرات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
