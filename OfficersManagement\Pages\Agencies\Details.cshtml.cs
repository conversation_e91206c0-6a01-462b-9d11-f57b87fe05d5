using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Agencies
{
    public class DetailsModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public DetailsModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public Agency Agency { get; set; } = default!;
        public int TotalDepartments { get; set; }
        public int TotalDivisions { get; set; }
        public int TotalOfficers { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var agency = await _context.Agencies
                .Include(a => a.Directorates)
                    .ThenInclude(d => d.Departments)
                        .ThenInclude(dept => dept.Divisions)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (agency == null)
            {
                return NotFound();
            }
            else
            {
                Agency = agency;
                
                // حساب الإحصائيات
                TotalDepartments = agency.Directorates.SelectMany(d => d.Departments).Count();
                TotalDivisions = agency.Directorates
                    .SelectMany(d => d.Departments)
                    .SelectMany(dept => dept.Divisions)
                    .Count();
                
                // حساب عدد الضباط في هذه الوكالة
                TotalOfficers = await _context.Officers
                    .Where(o => o.AgencyId == id)
                    .CountAsync();
            }
            return Page();
        }
    }
}
