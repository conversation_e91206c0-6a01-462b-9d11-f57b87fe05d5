using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Provinces
{
    public class IndexModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public IndexModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<Province> Provinces { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Provinces = await _context.Provinces
                .Include(p => p.Officers)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }
    }
}
