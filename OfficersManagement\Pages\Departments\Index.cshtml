@page
@model OfficersManagement.Pages.Departments.IndexModel
@{
    ViewData["Title"] = "إدارة الأقسام";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-users-cog me-2"></i>
                    إدارة الأقسام
                </h2>
                <a asp-page="./Create" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    إضافة قسم جديد
                </a>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الأقسام
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Departments.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>اسم القسم</th>
                                        <th>المديرية</th>
                                        <th>الوكالة</th>
                                        <th>عدد الشعب</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var department in Model.Departments)
                                    {
                                        <tr>
                                            <td>@department.Id</td>
                                            <td>
                                                <strong>@department.Name</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@department.Directorate?.Name</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">@department.Directorate?.Agency?.Name</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning">@department.Divisions.Count</span>
                                            </td>
                                            <td>
                                                @if (department.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="./Edit" asp-route-id="@department.Id" class="btn btn-warning btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-page="./Delete" asp-route-id="@department.Id" class="btn btn-danger btn-sm">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users-cog fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أقسام مسجلة</h5>
                            <p class="text-muted">يمكنك إضافة قسم جديد من خلال الزر أعلاه</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
