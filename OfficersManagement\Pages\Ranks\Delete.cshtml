@page
@model OfficersManagement.Pages.Ranks.DeleteModel
@{
    ViewData["Title"] = "حذف الرتبة";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-trash me-2"></i>
                        حذف الرتبة
                    </h4>
                </div>
                <div class="card-body">
                    @if (Model.Rank == null)
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الرتبة غير موجودة
                        </div>
                    }
                    else
                    {
                        @if (Model.Rank.Officers.Count > 0)
                        {
                            <div class="alert alert-danger">
                                <i class="fas fa-ban me-2"></i>
                                <strong>لا يمكن حذف هذه الرتبة!</strong><br>
                                هذه الرتبة مرتبطة بـ @Model.Rank.Officers.Count ضابط. 
                                يجب نقل الضباط إلى رتبة أخرى أو حذفهم أولاً قبل حذف الرتبة.
                            </div>
                            
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <a asp-page="./Index" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-1"></i>
                                            العودة للقائمة
                                        </a>
                                        <a asp-page="./Details" asp-route-id="@Model.Rank.Id" class="btn btn-info">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض التفاصيل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> هل أنت متأكد من حذف هذه الرتبة؟ هذا الإجراء لا يمكن التراجع عنه.
                            </div>

                            <div class="row">
                                <!-- المعلومات الأساسية -->
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">المعلومات الأساسية</h6>
                                        </div>
                                        <div class="card-body">
                                            <dl class="row">
                                                <dt class="col-sm-5">اسم الرتبة:</dt>
                                                <dd class="col-sm-7">
                                                    <span class="badge bg-success">@Model.Rank.Name</span>
                                                </dd>

                                                <dt class="col-sm-5">ترتيب الرتبة:</dt>
                                                <dd class="col-sm-7">
                                                    <span class="badge bg-primary">@Model.Rank.Order</span>
                                                </dd>

                                                <dt class="col-sm-5">الحالة:</dt>
                                                <dd class="col-sm-7">
                                                    @if (Model.Rank.IsActive)
                                                    {
                                                        <span class="badge bg-success">نشط</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-secondary">غير نشط</span>
                                                    }
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>

                                <!-- إحصائيات -->
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">الإحصائيات</h6>
                                        </div>
                                        <div class="card-body">
                                            <dl class="row">
                                                <dt class="col-sm-6">عدد الضباط:</dt>
                                                <dd class="col-sm-6">
                                                    <span class="badge bg-success">@Model.Rank.Officers.Count</span>
                                                </dd>

                                                <dt class="col-sm-6">تاريخ الإنشاء:</dt>
                                                <dd class="col-sm-6">
                                                    <small>@Model.Rank.CreatedAt.ToString("yyyy/MM/dd")</small>
                                                </dd>

                                                <dt class="col-sm-6">آخر تحديث:</dt>
                                                <dd class="col-sm-6">
                                                    <small>@Model.Rank.UpdatedAt.ToString("yyyy/MM/dd")</small>
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الوصف -->
                            @if (!string.IsNullOrEmpty(Model.Rank.Description))
                            {
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card mb-3">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0">الوصف</h6>
                                            </div>
                                            <div class="card-body">
                                                <p class="mb-0">@Model.Rank.Description</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }

                            <!-- أزرار الإجراءات -->
                            <form method="post">
                                <input type="hidden" asp-for="Rank.Id" />
                                
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex justify-content-between">
                                            <a asp-page="./Index" class="btn btn-secondary">
                                                <i class="fas fa-arrow-left me-1"></i>
                                                العودة للقائمة
                                            </a>
                                            <div>
                                                <a asp-page="./Details" asp-route-id="@Model.Rank.Id" class="btn btn-info me-2">
                                                    <i class="fas fa-eye me-1"></i>
                                                    عرض التفاصيل
                                                </a>
                                                <button type="submit" class="btn btn-danger" 
                                                        onclick="return confirm('هل أنت متأكد من حذف هذه الرتبة؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                    <i class="fas fa-trash me-1"></i>
                                                    تأكيد الحذف
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>
