﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using OfficersManagement.Data;

#nullable disable

namespace OfficersManagement.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250620193005_UpdateOfficerModel")]
    partial class UpdateOfficerModel
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("OfficersManagement.Models.Officer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("EmployeeNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("GrandfatherName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PlaceOfBirth")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Rank")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("SecondName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("StatisticalNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("ThirdName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Title")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Officers_EmployeeNumber");

                    b.HasIndex("StatisticalNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Officers_StatisticalNumber");

                    b.HasIndex("FirstName", "GrandfatherName")
                        .HasDatabaseName("IX_Officers_Name");

                    b.HasIndex("FirstName", "SecondName", "ThirdName", "GrandfatherName")
                        .IsUnique()
                        .HasDatabaseName("IX_Officers_FullName");

                    b.ToTable("Officers");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            DateOfBirth = new DateTime(1980, 5, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EmployeeNumber = "EMP001",
                            FirstName = "أحمد",
                            GrandfatherName = "الأحمد",
                            PlaceOfBirth = "بغداد",
                            Rank = "عقيد",
                            SecondName = "محمد",
                            StatisticalNumber = "STAT001",
                            ThirdName = "علي",
                            Title = "الدكتور",
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            DateOfBirth = new DateTime(1985, 8, 22, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EmployeeNumber = "EMP002",
                            FirstName = "فاطمة",
                            GrandfatherName = "الزهراء",
                            PlaceOfBirth = "البصرة",
                            Rank = "مقدم",
                            SecondName = "حسن",
                            StatisticalNumber = "STAT002",
                            ThirdName = "عبدالله",
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 3,
                            CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            DateOfBirth = new DateTime(1990, 12, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EmployeeNumber = "EMP003",
                            FirstName = "خالد",
                            GrandfatherName = "المحمود",
                            PlaceOfBirth = "الموصل",
                            Rank = "رائد",
                            SecondName = "عبدالرحمن",
                            StatisticalNumber = "STAT003",
                            ThirdName = "صالح",
                            UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
