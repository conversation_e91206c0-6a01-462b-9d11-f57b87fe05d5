using System.ComponentModel.DataAnnotations;

namespace OfficersManagement.Models
{
    public class Division
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الشعبة مطلوب")]
        [Display(Name = "اسم الشعبة")]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        [StringLength(500)]
        public string? Description { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Required]
        [Display(Name = "القسم")]
        public int DepartmentId { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Department? Department { get; set; }
    }
}
