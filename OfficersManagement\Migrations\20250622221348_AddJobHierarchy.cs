﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace OfficersManagement.Migrations
{
    /// <inheritdoc />
    public partial class AddJobHierarchy : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AgencyId",
                table: "Officers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DepartmentId",
                table: "Officers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DirectorateId",
                table: "Officers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DivisionId",
                table: "Officers",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Agencies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Agencies", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Directorates",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    AgencyId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Directorates", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Directorates_Agencies_AgencyId",
                        column: x => x.AgencyId,
                        principalTable: "Agencies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Departments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DirectorateId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Departments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Departments_Directorates_DirectorateId",
                        column: x => x.DirectorateId,
                        principalTable: "Directorates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Divisions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DepartmentId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Divisions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Divisions_Departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "Agencies",
                columns: new[] { "Id", "CreatedAt", "Description", "IsActive", "Name" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(3457), "وكالة وزارة الداخلية للشؤون الأمنية", true, "وكالة وزارة الداخلية" },
                    { 2, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(3775), "وكالة وزارة الدفاع للشؤون العسكرية", true, "وكالة وزارة الدفاع" },
                    { 3, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(3777), "وكالة الاستخبارات العراقية", true, "وكالة الاستخبارات" }
                });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "AgencyId", "DepartmentId", "DirectorateId", "DivisionId" },
                values: new object[] { null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "AgencyId", "DepartmentId", "DirectorateId", "DivisionId" },
                values: new object[] { null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "AgencyId", "DepartmentId", "DirectorateId", "DivisionId" },
                values: new object[] { null, null, null, null });

            migrationBuilder.InsertData(
                table: "Directorates",
                columns: new[] { "Id", "AgencyId", "CreatedAt", "Description", "IsActive", "Name" },
                values: new object[,]
                {
                    { 1, 1, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(6327), "مديرية عامة للشرطة", true, "مديرية الشرطة" },
                    { 2, 1, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(6589), "مديرية عامة للأمن العام", true, "مديرية الأمن العام" },
                    { 3, 1, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(6592), "مديرية عامة للجوازات", true, "مديرية الجوازات" },
                    { 4, 2, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(6594), "مديرية عامة للعمليات", true, "مديرية العمليات" },
                    { 5, 2, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(6597), "مديرية عامة للاستخبارات العسكرية", true, "مديرية الاستخبارات العسكرية" },
                    { 6, 3, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(6600), "مديرية عامة لمكافحة الإرهاب", true, "مديرية مكافحة الإرهاب" }
                });

            migrationBuilder.InsertData(
                table: "Departments",
                columns: new[] { "Id", "CreatedAt", "Description", "DirectorateId", "IsActive", "Name" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(8967), "قسم شرطة محافظة بغداد", 1, true, "قسم شرطة بغداد" },
                    { 2, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(9338), "قسم شرطة محافظة البصرة", 1, true, "قسم شرطة البصرة" },
                    { 3, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(9341), "قسم شرطة محافظة نينوى", 1, true, "قسم شرطة الموصل" },
                    { 4, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(9344), "قسم أمن محافظة بغداد", 2, true, "قسم أمن بغداد" },
                    { 5, new DateTime(2025, 6, 23, 1, 13, 47, 705, DateTimeKind.Local).AddTicks(9346), "قسم أمن محافظة البصرة", 2, true, "قسم أمن البصرة" }
                });

            migrationBuilder.InsertData(
                table: "Divisions",
                columns: new[] { "Id", "CreatedAt", "DepartmentId", "Description", "IsActive", "Name" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 6, 23, 1, 13, 47, 706, DateTimeKind.Local).AddTicks(3203), 1, "شعبة التحقيقات الجنائية", true, "شعبة التحقيقات" },
                    { 2, new DateTime(2025, 6, 23, 1, 13, 47, 706, DateTimeKind.Local).AddTicks(3628), 1, "شعبة الدوريات والمراقبة", true, "شعبة الدوريات" },
                    { 3, new DateTime(2025, 6, 23, 1, 13, 47, 706, DateTimeKind.Local).AddTicks(3630), 1, "شعبة الطوارئ والأزمات", true, "شعبة الطوارئ" },
                    { 4, new DateTime(2025, 6, 23, 1, 13, 47, 706, DateTimeKind.Local).AddTicks(3633), 4, "شعبة جمع وتحليل المعلومات", true, "شعبة المعلومات" },
                    { 5, new DateTime(2025, 6, 23, 1, 13, 47, 706, DateTimeKind.Local).AddTicks(3635), 4, "شعبة متابعة العناصر المشبوهة", true, "شعبة المتابعة" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Agencies_Name",
                table: "Agencies",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Departments_DirectorateId",
                table: "Departments",
                column: "DirectorateId");

            migrationBuilder.CreateIndex(
                name: "IX_Departments_Name_DirectorateId",
                table: "Departments",
                columns: new[] { "Name", "DirectorateId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Directorates_AgencyId",
                table: "Directorates",
                column: "AgencyId");

            migrationBuilder.CreateIndex(
                name: "IX_Directorates_Name_AgencyId",
                table: "Directorates",
                columns: new[] { "Name", "AgencyId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Divisions_DepartmentId",
                table: "Divisions",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_Divisions_Name_DepartmentId",
                table: "Divisions",
                columns: new[] { "Name", "DepartmentId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Divisions");

            migrationBuilder.DropTable(
                name: "Departments");

            migrationBuilder.DropTable(
                name: "Directorates");

            migrationBuilder.DropTable(
                name: "Agencies");

            migrationBuilder.DropColumn(
                name: "AgencyId",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "DepartmentId",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "DirectorateId",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "DivisionId",
                table: "Officers");
        }
    }
}
