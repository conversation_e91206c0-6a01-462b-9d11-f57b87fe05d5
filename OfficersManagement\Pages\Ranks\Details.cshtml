@page
@model OfficersManagement.Pages.Ranks.DetailsModel
@{
    ViewData["Title"] = "تفاصيل الرتبة";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        تفاصيل الرتبة
                    </h4>
                </div>
                <div class="card-body">
                    @if (Model.Rank == null)
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الرتبة غير موجودة
                        </div>
                    }
                    else
                    {
                        <div class="row">
                            <!-- المعلومات الأساسية -->
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">المعلومات الأساسية</h6>
                                    </div>
                                    <div class="card-body">
                                        <dl class="row">
                                            <dt class="col-sm-5">اسم الرتبة:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge bg-success fs-6">@Model.Rank.Name</span>
                                            </dd>

                                            <dt class="col-sm-5">ترتيب الرتبة:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge bg-primary">@Model.Rank.Order</span>
                                            </dd>

                                            <dt class="col-sm-5">الحالة:</dt>
                                            <dd class="col-sm-7">
                                                @if (Model.Rank.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>

                            <!-- إحصائيات -->
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">الإحصائيات</h6>
                                    </div>
                                    <div class="card-body">
                                        <dl class="row">
                                            <dt class="col-sm-6">عدد الضباط:</dt>
                                            <dd class="col-sm-6">
                                                <span class="badge bg-info fs-6">@Model.Rank.Officers.Count</span>
                                            </dd>

                                            <dt class="col-sm-6">تاريخ الإنشاء:</dt>
                                            <dd class="col-sm-6">
                                                <small>@Model.Rank.CreatedAt.ToString("yyyy/MM/dd")</small>
                                            </dd>

                                            <dt class="col-sm-6">آخر تحديث:</dt>
                                            <dd class="col-sm-6">
                                                <small>@Model.Rank.UpdatedAt.ToString("yyyy/MM/dd")</small>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الوصف -->
                        @if (!string.IsNullOrEmpty(Model.Rank.Description))
                        {
                            <div class="row">
                                <div class="col-12">
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">الوصف</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="mb-0">@Model.Rank.Description</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- قائمة الضباط -->
                        @if (Model.Rank.Officers.Any())
                        {
                            <div class="row">
                                <div class="col-12">
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">الضباط بهذه الرتبة (@Model.Rank.Officers.Count)</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>رقم الموظف</th>
                                                            <th>الاسم الكامل</th>
                                                            <th>الإجراءات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var officer in Model.Rank.Officers.Take(10))
                                                        {
                                                            <tr>
                                                                <td>
                                                                    <span class="badge bg-secondary">@officer.EmployeeNumber</span>
                                                                </td>
                                                                <td>@officer.FullName</td>
                                                                <td>
                                                                    <a asp-page="/Officers/Details" asp-route-id="@officer.Id" 
                                                                       class="btn btn-sm btn-outline-info">
                                                                        <i class="fas fa-eye"></i>
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                                @if (Model.Rank.Officers.Count > 10)
                                                {
                                                    <div class="text-center">
                                                        <small class="text-muted">
                                                            عرض 10 من أصل @Model.Rank.Officers.Count ضابط
                                                        </small>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-page="./Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للقائمة
                                    </a>
                                    <div>
                                        <a asp-page="./Edit" asp-route-id="@Model.Rank.Id" class="btn btn-warning me-2">
                                            <i class="fas fa-edit me-1"></i>
                                            تعديل
                                        </a>
                                        @if (Model.Rank.Officers.Count == 0)
                                        {
                                            <a asp-page="./Delete" asp-route-id="@Model.Rank.Id" class="btn btn-danger">
                                                <i class="fas fa-trash me-1"></i>
                                                حذف
                                            </a>
                                        }
                                        else
                                        {
                                            <button class="btn btn-outline-secondary" disabled title="لا يمكن حذف رتبة مرتبطة بضباط">
                                                <i class="fas fa-lock me-1"></i>
                                                محمية من الحذف
                                            </button>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
