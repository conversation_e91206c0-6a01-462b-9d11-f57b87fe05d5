using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Divisions
{
    public class EditModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public EditModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Division Division { get; set; } = default!;

        public SelectList DepartmentsList { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var division = await _context.Divisions.FirstOrDefaultAsync(m => m.Id == id);
            if (division == null)
            {
                return NotFound();
            }
            Division = division;
            await LoadDepartmentsAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadDepartmentsAsync();
                return Page();
            }

            try
            {
                _context.Attach(Division).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"تم تعديل الشعبة '{Division.Name}' بنجاح";
                return RedirectToPage("./Index");
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!DivisionExists(Division.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "حدث خطأ أثناء حفظ البيانات: " + ex.Message);
                await LoadDepartmentsAsync();
                return Page();
            }
        }

        private bool DivisionExists(int id)
        {
            return _context.Divisions.Any(e => e.Id == id);
        }

        private async Task LoadDepartmentsAsync()
        {
            var departments = await _context.Departments
                .Include(d => d.Directorate)
                    .ThenInclude(dir => dir!.Agency)
                .Where(d => d.IsActive)
                .OrderBy(d => d.Directorate!.Agency!.Name)
                .ThenBy(d => d.Directorate!.Name)
                .ThenBy(d => d.Name)
                .Select(d => new {
                    Id = d.Id,
                    Name = $"{d.Directorate!.Agency!.Name} - {d.Directorate.Name} - {d.Name}"
                })
                .ToListAsync();

            DepartmentsList = new SelectList(departments, "Id", "Name");
        }
    }
}
