@page
@model OfficersManagement.Pages.Officers.IndexModel
@{
    ViewData["Title"] = "إدارة الضباط والمنتسبين";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            إدارة الضباط والمنتسبين
                        </h4>
                        <a asp-page="./Create" class="btn btn-light">
                            <i class="fas fa-plus me-1"></i>
                            إضافة ضابط جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- شريط البحث -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="get">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="searchString" 
                                           value="@Model.CurrentFilter" placeholder="البحث بالاسم أو رقم الموظف...">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    @if (!string.IsNullOrEmpty(Model.CurrentFilter))
                                    {
                                        <a asp-page="./Index" class="btn btn-outline-danger">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    }
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge bg-info fs-6">
                                إجمالي الضباط: @Model.Officers.Count()
                            </span>
                        </div>
                    </div>

                    <!-- جدول الضباط -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الصورة</th>
                                    <th>
                                        <a asp-page="./Index" asp-route-sortOrder="@Model.EmployeeNumberSort"
                                           asp-route-currentFilter="@Model.CurrentFilter" class="text-white text-decoration-none">
                                            رقم الموظف
                                            @if (Model.CurrentSort == "employee_number")
                                            {
                                                <i class="fas fa-sort-up"></i>
                                            }
                                            else if (Model.CurrentSort == "employee_number_desc")
                                            {
                                                <i class="fas fa-sort-down"></i>
                                            }
                                            else
                                            {
                                                <i class="fas fa-sort"></i>
                                            }
                                        </a>
                                    </th>
                                    <th>الرقم الإحصائي</th>
                                    <th>الرتبة</th>
                                    <th>
                                        <a asp-page="./Index" asp-route-sortOrder="@Model.NameSort"
                                           asp-route-currentFilter="@Model.CurrentFilter" class="text-white text-decoration-none">
                                            الاسم الكامل
                                            @if (Model.CurrentSort == "name")
                                            {
                                                <i class="fas fa-sort-up"></i>
                                            }
                                            else if (Model.CurrentSort == "name_desc")
                                            {
                                                <i class="fas fa-sort-down"></i>
                                            }
                                            else
                                            {
                                                <i class="fas fa-sort"></i>
                                            }
                                        </a>
                                    </th>
                                    <th>محل الولادة</th>
                                    <th>
                                        <a asp-page="./Index" asp-route-sortOrder="@Model.DateSort"
                                           asp-route-currentFilter="@Model.CurrentFilter" class="text-white text-decoration-none">
                                            تاريخ الولادة
                                            @if (Model.CurrentSort == "date")
                                            {
                                                <i class="fas fa-sort-up"></i>
                                            }
                                            else if (Model.CurrentSort == "date_desc")
                                            {
                                                <i class="fas fa-sort-down"></i>
                                            }
                                            else
                                            {
                                                <i class="fas fa-sort"></i>
                                            }
                                        </a>
                                    </th>
                                    <th>العمر</th>
                                    <th>الحالة الاجتماعية</th>
                                    <th>فصيلة الدم</th>
                                    <th>الحالة الصحية</th>
                                    <th>محافظة السكن</th>
                                    <th>العنوان بالتفصيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var officer in Model.Officers)
                                {
                                    <tr>
                                        <td>
                                            @if (officer.PhotoData != null && officer.PhotoData.Length > 0)
                                            {
                                                <img src="data:@officer.PhotoContentType;base64,@Convert.ToBase64String(officer.PhotoData)"
                                                     alt="صورة @officer.FullName"
                                                     class="rounded-circle"
                                                     style="width: 40px; height: 40px; object-fit: cover;" />
                                            }
                                            else
                                            {
                                                <i class="fas fa-user-circle fa-2x text-muted"></i>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">@officer.EmployeeNumber</span>
                                        </td>
                                        <td>@officer.StatisticalNumber</td>
                                        <td>
                                            <span class="badge bg-success">@(officer.Rank?.Name ?? "غير محدد")</span>
                                        </td>
                                        <td>
                                            <strong>@officer.FullName</strong>
                                        </td>
                                        <td>@(officer.PlaceOfBirth ?? "غير محدد")</td>
                                        <td>@officer.DateOfBirth.ToString("yyyy/MM/dd")</td>
                                        <td>
                                            <small class="text-info">@officer.Age</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">@officer.MaritalStatus</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger">@officer.BloodType</span>
                                        </td>

                                        <td>
                                            <span class="badge @(officer.HealthStatus == "سليم" ? "bg-success" : officer.HealthStatus == "مريض" ? "bg-warning" : "bg-secondary")">@officer.HealthStatus</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">@(officer.Province?.Name ?? "غير محدد")</small>
                                        </td>
                                        <td>
                                            <small class="text-muted" title="@officer.DetailedAddress">
                                                @if (officer.DetailedAddress.Length > 50)
                                                {
                                                    @(officer.DetailedAddress.Substring(0, 50) + "...")
                                                }
                                                else
                                                {
                                                    @officer.DetailedAddress
                                                }
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-page="./Details" asp-route-id="@officer.Id"
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@officer.Id"
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-page="./Delete" asp-route-id="@officer.Id"
                                                   class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Officers.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات ضباط</h5>
                            <p class="text-muted">قم بإضافة ضابط جديد للبدء</p>
                            <a asp-page="./Create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة ضابط جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // تأكيد الحذف
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('a[href*="/Delete"]');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (!confirm('هل أنت متأكد من حذف هذا الضابط؟')) {
                        e.preventDefault();
                    }
                });
            });
        });
    </script>
}
