@page
@model OfficersManagement.Pages.Agencies.CreateModel
@{
    ViewData["Title"] = "إضافة وكالة جديدة";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-building me-2"></i>
                    إضافة وكالة جديدة
                </h2>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        بيانات الوكالة الجديدة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Agency.Name" class="form-label">اسم الوكالة</label>
                                <input asp-for="Agency.Name" class="form-control" placeholder="أدخل اسم الوكالة" />
                                <span asp-validation-for="Agency.Name" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input asp-for="Agency.IsActive" class="form-check-input" />
                                    <label asp-for="Agency.IsActive" class="form-check-label">
                                        وكالة نشطة
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Agency.Description" class="form-label">الوصف</label>
                            <textarea asp-for="Agency.Description" class="form-control" rows="3" placeholder="أدخل وصف الوكالة (اختياري)"></textarea>
                            <span asp-validation-for="Agency.Description" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                حفظ الوكالة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // إرسال رسالة للنافذة الأصلية عند النجاح
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من وجود رسالة نجاح
            const successMessage = '@TempData["SuccessMessage"]';
            if (successMessage && successMessage.trim() !== '') {
                // إرسال رسالة للنافذة الأصلية
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'AGENCY_CREATED',
                        message: successMessage
                    }, '*');

                    // إغلاق النافذة بعد ثانيتين
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                }
            }
        });
    </script>
}
