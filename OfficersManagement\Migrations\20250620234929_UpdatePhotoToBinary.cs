﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OfficersManagement.Migrations
{
    /// <inheritdoc />
    public partial class UpdatePhotoToBinary : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PhotoPath",
                table: "Officers");

            migrationBuilder.AddColumn<string>(
                name: "PhotoContentType",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "PhotoData",
                table: "Officers",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "PhotoContentType", "PhotoData" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "PhotoContentType", "PhotoData" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "PhotoContentType", "PhotoData" },
                values: new object[] { null, null });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PhotoContentType",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "PhotoData",
                table: "Officers");

            migrationBuilder.AddColumn<string>(
                name: "PhotoPath",
                table: "Officers",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 1,
                column: "PhotoPath",
                value: null);

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 2,
                column: "PhotoPath",
                value: null);

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 3,
                column: "PhotoPath",
                value: null);
        }
    }
}
