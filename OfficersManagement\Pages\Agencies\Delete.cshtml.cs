using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Agencies
{
    public class DeleteModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public DeleteModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Agency Agency { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var agency = await _context.Agencies
                .Include(a => a.Directorates)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (agency == null)
            {
                return NotFound();
            }
            else
            {
                Agency = agency;
            }
            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            try
            {
                var agency = await _context.Agencies
                    .Include(a => a.Directorates)
                    .FirstOrDefaultAsync(a => a.Id == id);

                if (agency != null)
                {
                    // التحقق من وجود مديريات تابعة
                    if (agency.Directorates.Any())
                    {
                        TempData["ErrorMessage"] = "لا يمكن حذف الوكالة لأنها تحتوي على مديريات تابعة. يرجى حذف المديريات أولاً.";
                        return RedirectToPage("./Index");
                    }

                    _context.Agencies.Remove(agency);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = $"تم حذف الوكالة '{agency.Name}' بنجاح";
                }

                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف الوكالة: " + ex.Message;
                return RedirectToPage("./Index");
            }
        }
    }
}
