using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Officers
{
    public class IndexModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public IndexModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<Officer> Officers { get; set; } = default!;
        public string CurrentFilter { get; set; } = string.Empty;
        public string CurrentSort { get; set; } = string.Empty;

        public string NameSort { get; set; } = string.Empty;
        public string DateSort { get; set; } = string.Empty;
        public string EmployeeNumberSort { get; set; } = string.Empty;

        public async Task OnGetAsync(string sortOrder, string currentFilter, string searchString)
        {
            CurrentSort = sortOrder;
            NameSort = string.IsNullOrEmpty(sortOrder) ? "name_desc" : "";
            DateSort = sortOrder == "date" ? "date_desc" : "date";
            EmployeeNumberSort = sortOrder == "employee_number" ? "employee_number_desc" : "employee_number";

            if (searchString != null)
            {
                CurrentFilter = searchString;
            }
            else
            {
                searchString = currentFilter;
                CurrentFilter = currentFilter ?? string.Empty;
            }

            IQueryable<Officer> officersIQ = from o in _context.Officers
                                           .Include(o => o.Rank)
                                           .Include(o => o.Province)
                                           select o;

            if (!string.IsNullOrEmpty(searchString))
            {
                officersIQ = officersIQ.Where(o =>
                    o.FirstName.Contains(searchString) ||
                    o.SecondName.Contains(searchString) ||
                    o.ThirdName.Contains(searchString) ||
                    o.FourthName.Contains(searchString) ||
                    o.Title.Contains(searchString) ||
                    o.EmployeeNumber.Contains(searchString) ||
                    o.StatisticalNumber.Contains(searchString) ||
                    (o.Rank != null && o.Rank.Name.Contains(searchString)));
            }

            switch (sortOrder)
            {
                case "name_desc":
                    officersIQ = officersIQ.OrderByDescending(o => o.FirstName);
                    break;
                case "date":
                    officersIQ = officersIQ.OrderBy(o => o.DateOfBirth);
                    break;
                case "date_desc":
                    officersIQ = officersIQ.OrderByDescending(o => o.DateOfBirth);
                    break;
                case "employee_number":
                    officersIQ = officersIQ.OrderBy(o => o.EmployeeNumber);
                    break;
                case "employee_number_desc":
                    officersIQ = officersIQ.OrderByDescending(o => o.EmployeeNumber);
                    break;
                default:
                    officersIQ = officersIQ.OrderBy(o => o.FirstName);
                    break;
            }

            Officers = await officersIQ.AsNoTracking().ToListAsync();
        }
    }
}
