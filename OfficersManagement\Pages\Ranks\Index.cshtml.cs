using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Ranks
{
    public class IndexModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public IndexModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<Rank> Ranks { get; set; } = default!;
        public string CurrentFilter { get; set; } = string.Empty;
        public string CurrentSort { get; set; } = string.Empty;

        public string NameSort { get; set; } = string.Empty;
        public string OrderSort { get; set; } = string.Empty;

        public async Task OnGetAsync(string sortOrder, string currentFilter, string searchString)
        {
            CurrentSort = sortOrder;
            NameSort = string.IsNullOrEmpty(sortOrder) ? "name_desc" : "";
            OrderSort = sortOrder == "order" ? "order_desc" : "order";

            if (searchString != null)
            {
                CurrentFilter = searchString;
            }
            else
            {
                searchString = currentFilter;
                CurrentFilter = currentFilter ?? string.Empty;
            }

            IQueryable<Rank> ranksIQ = from r in _context.Ranks.Include(r => r.Officers)
                                       select r;

            if (!string.IsNullOrEmpty(searchString))
            {
                ranksIQ = ranksIQ.Where(r => 
                    r.Name.Contains(searchString) ||
                    (r.Description != null && r.Description.Contains(searchString)));
            }

            switch (sortOrder)
            {
                case "name_desc":
                    ranksIQ = ranksIQ.OrderByDescending(r => r.Name);
                    break;
                case "order":
                    ranksIQ = ranksIQ.OrderBy(r => r.Order);
                    break;
                case "order_desc":
                    ranksIQ = ranksIQ.OrderByDescending(r => r.Order);
                    break;
                default:
                    ranksIQ = ranksIQ.OrderBy(r => r.Order);
                    break;
            }

            Ranks = await ranksIQ.AsNoTracking().ToListAsync();
        }
    }
}
