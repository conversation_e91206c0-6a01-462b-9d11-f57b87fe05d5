using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;
using OfficersManagement.Services;

namespace OfficersManagement.Pages.Officers
{
    public class CreateModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly IEmployeeNumberService _employeeNumberService;
        private readonly ImageCompressionService _imageCompressionService;

        public CreateModel(ApplicationDbContext context, IEmployeeNumberService employeeNumberService, ImageCompressionService imageCompressionService)
        {
            _context = context;
            _employeeNumberService = employeeNumberService;
            _imageCompressionService = imageCompressionService;
        }

        public SelectList RanksList { get; set; } = default!;
        public SelectList ProvincesList { get; set; } = default!;
        public SelectList DistrictsList { get; set; } = default!;
        public SelectList SubdistrictsList { get; set; } = default!;
        public SelectList VillagesList { get; set; } = default!;
        public SelectList AgenciesList { get; set; } = default!;
        public SelectList DirectoratesList { get; set; } = default!;
        public SelectList DepartmentsList { get; set; } = default!;
        public SelectList DivisionsList { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            await LoadRanksAndProvincesAsync();
            return Page();
        }

        [BindProperty]
        public Officer Officer { get; set; } = default!;

        [BindProperty]
        public IFormFile? PhotoFile { get; set; }

        [BindProperty]
        public string? PhotoDataUrl { get; set; }

        private async Task LoadRanksAndProvincesAsync()
        {
            var ranks = await _context.Ranks
                .Where(r => r.IsActive)
                .OrderBy(r => r.Order)
                .ToListAsync();

            RanksList = new SelectList(ranks, "Id", "Name");

            var provinces = await _context.Provinces
                .OrderBy(p => p.Name)
                .ToListAsync();

            ProvincesList = new SelectList(provinces, "Id", "Name");

            var agencies = await _context.Agencies
                .Where(a => a.IsActive)
                .OrderBy(a => a.Name)
                .ToListAsync();

            AgenciesList = new SelectList(agencies, "Id", "Name");

            // تهيئة القوائم الفارغة
            DistrictsList = new SelectList(new List<object>(), "Id", "Name");
            SubdistrictsList = new SelectList(new List<object>(), "Id", "Name");
            VillagesList = new SelectList(new List<object>(), "Id", "Name");
            DirectoratesList = new SelectList(new List<object>(), "Id", "Name");
            DepartmentsList = new SelectList(new List<object>(), "Id", "Name");
            DivisionsList = new SelectList(new List<object>(), "Id", "Name");
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadRanksAndProvincesAsync();
                return Page();
            }

            // التحقق من عدم تكرار الاسم الكامل
            var existingOfficerByFullName = await _context.Officers
                .FirstOrDefaultAsync(o => o.FirstName == Officer.FirstName &&
                                         o.SecondName == Officer.SecondName &&
                                         o.ThirdName == Officer.ThirdName &&
                                         o.FourthName == Officer.FourthName);

            if (existingOfficerByFullName != null)
            {
                ModelState.AddModelError("", "يوجد ضابط بنفس الاسم الكامل مسبقاً");
                await LoadRanksAndProvincesAsync();
                return Page();
            }

            // التحقق من عدم تكرار الرقم الإحصائي
            var existingOfficerByStatisticalNumber = await _context.Officers
                .FirstOrDefaultAsync(o => o.StatisticalNumber == Officer.StatisticalNumber);

            if (existingOfficerByStatisticalNumber != null)
            {
                ModelState.AddModelError("Officer.StatisticalNumber", "يوجد ضابط بنفس الرقم الإحصائي مسبقاً");
                await LoadRanksAndProvincesAsync();
                return Page();
            }

            // التحقق من صحة تاريخ الولادة
            if (Officer.DateOfBirth > DateTime.Today)
            {
                ModelState.AddModelError("Officer.DateOfBirth", "تاريخ الولادة لا يمكن أن يكون في المستقبل");
                await LoadRanksAndProvincesAsync();
                return Page();
            }

            // التحقق من العمر المنطقي (بين 18 و 65 سنة)
            var age = DateTime.Today.Year - Officer.DateOfBirth.Year;
            if (Officer.DateOfBirth.Date > DateTime.Today.AddYears(-age))
                age--;

            if (age < 18)
            {
                ModelState.AddModelError("Officer.DateOfBirth", "العمر يجب أن يكون 18 سنة على الأقل");
                await LoadRanksAndProvincesAsync();
                return Page();
            }

            if (age > 65)
            {
                ModelState.AddModelError("Officer.DateOfBirth", "العمر يجب أن يكون أقل من 65 سنة");
                await LoadRanksAndProvincesAsync();
                return Page();
            }

            // معالجة رفع الصورة
            await HandlePhotoUploadAsync();

            // توليد رقم الموظف تلقائياً فقط
            Officer.EmployeeNumber = await _employeeNumberService.GenerateEmployeeNumberAsync();

            Officer.CreatedAt = DateTime.Now;
            Officer.UpdatedAt = DateTime.Now;

            _context.Officers.Add(Officer);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = $"تم إضافة الضابط بنجاح - رقم الموظف: {Officer.EmployeeNumber}";
            return RedirectToPage("./Index");
        }

        private async Task HandlePhotoUploadAsync()
        {
            try
            {
                byte[]? imageBytes = null;
                string contentType = "image/jpeg";

                // معالجة الصورة من الكاميرا (PhotoDataUrl)
                if (!string.IsNullOrEmpty(PhotoDataUrl))
                {
                    if (PhotoDataUrl.Contains(","))
                    {
                        var base64Data = PhotoDataUrl.Split(',')[1]; // إزالة البادئة data:image/...;base64,
                        imageBytes = Convert.FromBase64String(base64Data);

                        // تحديد نوع المحتوى من البادئة
                        var prefix = PhotoDataUrl.Split(',')[0];
                        if (prefix.Contains("image/png"))
                            contentType = "image/png";
                        else if (prefix.Contains("image/gif"))
                            contentType = "image/gif";
                        else
                            contentType = "image/jpeg";
                    }
                }
                // معالجة الصورة من رفع الملف (PhotoFile)
                else if (PhotoFile != null && PhotoFile.Length > 0)
                {
                    using var memoryStream = new MemoryStream();
                    await PhotoFile.CopyToAsync(memoryStream);
                    imageBytes = memoryStream.ToArray();
                    contentType = PhotoFile.ContentType;
                }

                // حفظ البيانات الثنائية للصورة مع الضغط
                if (imageBytes != null && imageBytes.Length > 0)
                {
                    // ضغط الصورة
                    var compressedBytes = _imageCompressionService.CompressImage(imageBytes, contentType);

                    Officer.PhotoData = compressedBytes;
                    Officer.PhotoContentType = contentType;
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل رفع الصورة، لا نوقف العملية
                Console.WriteLine($"خطأ في رفع الصورة: {ex.Message}");
                Officer.PhotoData = null;
                Officer.PhotoContentType = null;
            }
        }

        // API endpoints للحصول على البيانات المترابطة
        public async Task<IActionResult> OnGetDirectoratesAsync(int agencyId)
        {
            var directorates = await _context.Directorates
                .Where(d => d.AgencyId == agencyId && d.IsActive)
                .OrderBy(d => d.Name)
                .Select(d => new { d.Id, d.Name })
                .ToListAsync();

            return new JsonResult(directorates);
        }

        public async Task<IActionResult> OnGetDepartmentsAsync(int directorateId)
        {
            var departments = await _context.Departments
                .Where(d => d.DirectorateId == directorateId && d.IsActive)
                .OrderBy(d => d.Name)
                .Select(d => new { d.Id, d.Name })
                .ToListAsync();

            return new JsonResult(departments);
        }

        public async Task<IActionResult> OnGetDivisionsAsync(int departmentId)
        {
            var divisions = await _context.Divisions
                .Where(d => d.DepartmentId == departmentId && d.IsActive)
                .OrderBy(d => d.Name)
                .Select(d => new { d.Id, d.Name })
                .ToListAsync();

            return new JsonResult(divisions);
        }
    }
}
