using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;

namespace OfficersManagement.Controllers
{
    [Route("api")]
    [ApiController]
    public class LocationApiController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public LocationApiController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet("districts/{provinceId}")]
        public async Task<IActionResult> GetDistricts(int provinceId)
        {
            try
            {
                var districts = await _context.Districts
                    .Where(d => d.ProvinceId == provinceId)
                    .OrderBy(d => d.Name)
                    .Select(d => new { d.Id, d.Name })
                    .ToListAsync();

                return Ok(districts);
            }
            catch (Exception ex)
            {
                return BadRequest($"خطأ في تحميل الأقضية: {ex.Message}");
            }
        }

        [HttpGet("subdistricts/{districtId}")]
        public async Task<IActionResult> GetSubdistricts(int districtId)
        {
            try
            {
                var subdistricts = await _context.Subdistricts
                    .Where(s => s.DistrictId == districtId)
                    .OrderBy(s => s.Name)
                    .Select(s => new { s.Id, s.Name })
                    .ToListAsync();

                return Ok(subdistricts);
            }
            catch (Exception ex)
            {
                return BadRequest($"خطأ في تحميل النواحي: {ex.Message}");
            }
        }

        [HttpGet("villages/{subdistrictId}")]
        public async Task<IActionResult> GetVillages(int subdistrictId)
        {
            try
            {
                var villages = await _context.Villages
                    .Where(v => v.SubdistrictId == subdistrictId)
                    .OrderBy(v => v.Name)
                    .Select(v => new { v.Id, v.Name })
                    .ToListAsync();

                return Ok(villages);
            }
            catch (Exception ex)
            {
                return BadRequest($"خطأ في تحميل القرى: {ex.Message}");
            }
        }
    }
}
