@page
@model OfficersManagement.Pages.Agencies.DetailsModel
@{
    ViewData["Title"] = "تفاصيل الوكالة";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الوكالة
                </h2>
                <div>
                    <a asp-page="./Edit" asp-route-id="@Model.Agency.Id" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-2"></i>
                        تعديل
                    </a>
                    <a asp-page="./Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- معلومات الوكالة -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-building me-2"></i>
                                معلومات الوكالة
                            </h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الرقم:</strong></td>
                                    <td>@Model.Agency.Id</td>
                                </tr>
                                <tr>
                                    <td><strong>الاسم:</strong></td>
                                    <td><strong class="text-primary">@Model.Agency.Name</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>الوصف:</strong></td>
                                    <td>@Model.Agency.Description</td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        @if (Model.Agency.IsActive)
                                        {
                                            <span class="badge bg-success">نشط</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">غير نشط</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>@Model.Agency.CreatedAt.ToString("yyyy/MM/dd HH:mm")</td>
                                </tr>

                            </table>
                        </div>
                    </div>
                </div>

                <!-- المديريات التابعة -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-sitemap me-2"></i>
                                المديريات التابعة
                                <span class="badge bg-light text-dark ms-2">@Model.Agency.Directorates.Count</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (Model.Agency.Directorates.Any())
                            {
                                <div class="list-group">
                                    @foreach (var directorate in Model.Agency.Directorates.OrderBy(d => d.Name))
                                    {
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">@directorate.Name</h6>
                                                <small class="text-muted">@directorate.Description</small>
                                            </div>
                                            <div>
                                                @if (directorate.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                                <span class="badge bg-warning text-dark ms-1">@directorate.Departments.Count أقسام</span>
                                            </div>
                                        </div>
                                    }
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted">لا توجد مديريات تابعة</h6>
                                    <a asp-page="/Directorates/Create" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i>
                                        إضافة مديرية جديدة
                                    </a>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                إحصائيات الهيكل التنظيمي
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h3 class="text-info">@Model.Agency.Directorates.Count</h3>
                                        <small class="text-muted">المديريات</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h3 class="text-warning">@Model.TotalDepartments</h3>
                                        <small class="text-muted">الأقسام</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h3 class="text-success">@Model.TotalDivisions</h3>
                                        <small class="text-muted">الشعب</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h3 class="text-primary">@Model.TotalOfficers</h3>
                                        <small class="text-muted">الضباط</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
