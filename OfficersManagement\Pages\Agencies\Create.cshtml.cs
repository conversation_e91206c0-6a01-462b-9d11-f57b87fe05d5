using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Agencies
{
    public class CreateModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public CreateModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Agency Agency { get; set; } = default!;

        public IActionResult OnGet()
        {
            Agency = new Agency();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                _context.Agencies.Add(Agency);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"تم إضافة الوكالة '{Agency.Name}' بنجاح";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "حدث خطأ أثناء حفظ البيانات: " + ex.Message);
                return Page();
            }
        }
    }
}
