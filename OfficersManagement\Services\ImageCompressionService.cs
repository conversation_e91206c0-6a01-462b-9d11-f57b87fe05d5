using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Drawing2D;

namespace OfficersManagement.Services
{
    public class ImageCompressionService
    {
        private const int MaxWidth = 300;  // تقليل العرض الأقصى أكثر
        private const int MaxHeight = 300; // تقليل الارتفاع الأقصى أكثر
        private const long MaxFileSizeBytes = 80 * 1024; // 80 KB بدلاً من 100 KB

        public byte[] CompressImage(byte[] imageBytes, string contentType)
        {
            try
            {
                using var inputStream = new MemoryStream(imageBytes);
                using var originalImage = Image.FromStream(inputStream);

                // تحديد الأبعاد الجديدة مع الحفاظ على النسبة
                var (newWidth, newHeight) = CalculateNewDimensions(originalImage.Width, originalImage.Height);

                // إنشاء صورة مضغوطة
                using var compressedImage = new Bitmap(newWidth, newHeight);
                using var graphics = Graphics.FromImage(compressedImage);

                // تحسين جودة الرسم للضغط الأمثل
                graphics.CompositingQuality = CompositingQuality.HighSpeed; // تغيير لسرعة أعلى وحجم أقل
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = SmoothingMode.AntiAlias; // تحسين للحجم

                // رسم الصورة المضغوطة
                graphics.DrawImage(originalImage, 0, 0, newWidth, newHeight);

                // حفظ الصورة بجودة مضغوطة - تحويل دائماً إلى JPEG للحجم الأصغر
                return SaveCompressedImage(compressedImage, "image/jpeg");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في ضغط الصورة: {ex.Message}");
                // في حالة فشل الضغط، محاولة ضغط بسيط
                return CompressImageSimple(imageBytes);
            }
        }

        private (int width, int height) CalculateNewDimensions(int originalWidth, int originalHeight)
        {
            // إذا كانت الصورة أصغر من الحد الأقصى، لا نغير الأبعاد
            if (originalWidth <= MaxWidth && originalHeight <= MaxHeight)
            {
                return (originalWidth, originalHeight);
            }

            // حساب النسبة للحفاظ على شكل الصورة
            double ratioX = (double)MaxWidth / originalWidth;
            double ratioY = (double)MaxHeight / originalHeight;
            double ratio = Math.Min(ratioX, ratioY);

            int newWidth = (int)(originalWidth * ratio);
            int newHeight = (int)(originalHeight * ratio);

            return (newWidth, newHeight);
        }

        private byte[] SaveCompressedImage(Bitmap image, string contentType)
        {
            // استخدام JPEG دائماً للحصول على أصغر حجم
            ImageCodecInfo codec = GetEncoder(ImageFormat.Jpeg);

            using var outputStream = new MemoryStream();

            if (codec != null)
            {
                // بدء بجودة منخفضة للحصول على حجم أصغر
                var encoderParams = new EncoderParameters(1);
                encoderParams.Param[0] = new EncoderParameter(Encoder.Quality, 60L); // جودة 60%

                image.Save(outputStream, codec, encoderParams);
            }
            else
            {
                // fallback
                image.Save(outputStream, ImageFormat.Jpeg);
            }

            var compressedBytes = outputStream.ToArray();

            // إذا كان الحجم ما زال كبيراً، نقلل الجودة أكثر
            if (compressedBytes.Length > MaxFileSizeBytes)
            {
                return CompressJpegFurther(image, codec);
            }

            Console.WriteLine($"تم ضغط الصورة - الحجم النهائي: {compressedBytes.Length / 1024.0:F1} KB");
            return compressedBytes;
        }

        private byte[] CompressJpegFurther(Bitmap image, ImageCodecInfo codec)
        {
            // تجربة جودات أقل للحصول على حجم أصغر
            long[] qualityLevels = { 50, 40, 30, 25, 20, 15, 10 };

            foreach (var quality in qualityLevels)
            {
                using var outputStream = new MemoryStream();
                var encoderParams = new EncoderParameters(1);
                encoderParams.Param[0] = new EncoderParameter(Encoder.Quality, quality);

                image.Save(outputStream, codec, encoderParams);
                var compressedBytes = outputStream.ToArray();

                if (compressedBytes.Length <= MaxFileSizeBytes)
                {
                    Console.WriteLine($"تم ضغط الصورة بجودة {quality}% - الحجم: {compressedBytes.Length / 1024.0:F1} KB");
                    return compressedBytes;
                }
            }

            // إذا لم نتمكن من الوصول للحجم المطلوب، نقلل الأبعاد أكثر
            return CompressWithSmallerDimensions(image, codec);
        }

        private byte[] CompressWithSmallerDimensions(Bitmap originalImage, ImageCodecInfo codec)
        {
            // تقليل الأبعاد أكثر إذا لم ينجح ضغط الجودة
            int newWidth = Math.Max(150, originalImage.Width / 2);
            int newHeight = Math.Max(150, originalImage.Height / 2);

            using var smallerImage = new Bitmap(newWidth, newHeight);
            using var graphics = Graphics.FromImage(smallerImage);

            graphics.CompositingQuality = CompositingQuality.HighSpeed;
            graphics.InterpolationMode = InterpolationMode.Low; // جودة أقل للحجم الأصغر
            graphics.SmoothingMode = SmoothingMode.HighSpeed;

            graphics.DrawImage(originalImage, 0, 0, newWidth, newHeight);

            using var outputStream = new MemoryStream();
            var encoderParams = new EncoderParameters(1);
            encoderParams.Param[0] = new EncoderParameter(Encoder.Quality, 15L); // جودة منخفضة جداً

            smallerImage.Save(outputStream, codec, encoderParams);
            var result = outputStream.ToArray();

            Console.WriteLine($"تم ضغط الصورة بأبعاد أصغر ({newWidth}x{newHeight}) - الحجم: {result.Length / 1024.0:F1} KB");
            return result;
        }

        private ImageCodecInfo GetEncoder(ImageFormat format)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageDecoders();
            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.FormatID == format.Guid)
                {
                    return codec;
                }
            }
            return null;
        }

        private byte[] CompressImageSimple(byte[] imageBytes)
        {
            try
            {
                // ضغط بسيط في حالة فشل الضغط المتقدم
                if (imageBytes.Length <= MaxFileSizeBytes)
                    return imageBytes;

                // تقليل الحجم بنسبة ثابتة
                double ratio = Math.Sqrt((double)MaxFileSizeBytes / imageBytes.Length);

                using var inputStream = new MemoryStream(imageBytes);
                using var originalImage = Image.FromStream(inputStream);

                int newWidth = Math.Max(80, (int)(originalImage.Width * ratio));
                int newHeight = Math.Max(80, (int)(originalImage.Height * ratio));

                using var resizedImage = new Bitmap(newWidth, newHeight);
                using var graphics = Graphics.FromImage(resizedImage);

                graphics.DrawImage(originalImage, 0, 0, newWidth, newHeight);

                using var outputStream = new MemoryStream();
                resizedImage.Save(outputStream, ImageFormat.Jpeg);

                return outputStream.ToArray();
            }
            catch
            {
                // في حالة فشل كل شيء، إرجاع جزء من البيانات الأصلية
                return imageBytes.Take((int)MaxFileSizeBytes).ToArray();
            }
        }

        public string GetImageInfo(byte[] imageBytes)
        {
            try
            {
                using var stream = new MemoryStream(imageBytes);
                using var image = Image.FromStream(stream);

                double sizeInKB = imageBytes.Length / 1024.0;
                return $"الأبعاد: {image.Width}x{image.Height} - الحجم: {sizeInKB:F1} KB";
            }
            catch
            {
                return $"الحجم: {imageBytes.Length / 1024.0:F1} KB";
            }
        }
    }
}
