using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Departments
{
    public class CreateModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public CreateModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Department Department { get; set; } = default!;

        public SelectList DirectoratesList { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            await LoadDirectoratesAsync();
            Department = new Department();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadDirectoratesAsync();
                return Page();
            }

            try
            {
                _context.Departments.Add(Department);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"تم إضافة القسم '{Department.Name}' بنجاح";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "حدث خطأ أثناء حفظ البيانات: " + ex.Message);
                await LoadDirectoratesAsync();
                return Page();
            }
        }

        private async Task LoadDirectoratesAsync()
        {
            var directorates = await _context.Directorates
                .Include(d => d.Agency)
                .Where(d => d.IsActive)
                .OrderBy(d => d.Agency!.Name)
                .ThenBy(d => d.Name)
                .Select(d => new { 
                    Id = d.Id, 
                    Name = $"{d.Agency!.Name} - {d.Name}" 
                })
                .ToListAsync();

            DirectoratesList = new SelectList(directorates, "Id", "Name");
        }
    }
}
