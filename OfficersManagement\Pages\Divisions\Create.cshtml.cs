using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Divisions
{
    public class CreateModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public CreateModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Division Division { get; set; } = default!;

        public SelectList DepartmentsList { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            await LoadDepartmentsAsync();
            Division = new Division();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadDepartmentsAsync();
                return Page();
            }

            try
            {
                _context.Divisions.Add(Division);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"تم إضافة الشعبة '{Division.Name}' بنجاح";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "حدث خطأ أثناء حفظ البيانات: " + ex.Message);
                await LoadDepartmentsAsync();
                return Page();
            }
        }

        private async Task LoadDepartmentsAsync()
        {
            var departments = await _context.Departments
                .Include(d => d.Directorate)
                    .ThenInclude(dir => dir!.Agency)
                .Where(d => d.IsActive)
                .OrderBy(d => d.Directorate!.Agency!.Name)
                .ThenBy(d => d.Directorate!.Name)
                .ThenBy(d => d.Name)
                .Select(d => new { 
                    Id = d.Id, 
                    Name = $"{d.Directorate!.Agency!.Name} - {d.Directorate.Name} - {d.Name}" 
                })
                .ToListAsync();

            DepartmentsList = new SelectList(departments, "Id", "Name");
        }
    }
}
