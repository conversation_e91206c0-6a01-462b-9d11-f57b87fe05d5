@page
@model OfficersManagement.Pages.Agencies.DeleteModel
@{
    ViewData["Title"] = "حذف الوكالة";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-danger">
                    <i class="fas fa-trash me-2"></i>
                    حذف الوكالة
                </h2>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف الوكالة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هل أنت متأكد من حذف هذه الوكالة؟ هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات الوكالة:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الرقم:</strong></td>
                                    <td>@Model.Agency.Id</td>
                                </tr>
                                <tr>
                                    <td><strong>الاسم:</strong></td>
                                    <td>@Model.Agency.Name</td>
                                </tr>
                                <tr>
                                    <td><strong>الوصف:</strong></td>
                                    <td>@Model.Agency.Description</td>
                                </tr>
                                <tr>
                                    <td><strong>عدد المديريات:</strong></td>
                                    <td><span class="badge bg-info">@Model.Agency.Directorates.Count</span></td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        @if (Model.Agency.IsActive)
                                        {
                                            <span class="badge bg-success">نشط</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">غير نشط</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>@Model.Agency.CreatedAt.ToString("yyyy/MM/dd")</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if (Model.Agency.Directorates.Any())
                    {
                        <div class="alert alert-danger mt-3">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>تنبيه:</strong> هذه الوكالة تحتوي على @Model.Agency.Directorates.Count مديرية. حذف الوكالة سيؤثر على جميع المديريات والأقسام والشعب التابعة لها.
                        </div>
                    }

                    <form method="post" class="mt-4">
                        <input type="hidden" asp-for="Agency.Id" />
                        <div class="d-flex justify-content-end gap-2">
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>
                                تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
