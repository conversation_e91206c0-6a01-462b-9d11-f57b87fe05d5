﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace OfficersManagement.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Officers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EmployeeNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    StatisticalNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Rank = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    FirstName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    SecondName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ThirdName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    LastName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Title = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    PlaceOfBirth = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    DateOfBirth = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Officers", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "Officers",
                columns: new[] { "Id", "CreatedAt", "DateOfBirth", "EmployeeNumber", "FirstName", "LastName", "PlaceOfBirth", "Rank", "SecondName", "StatisticalNumber", "ThirdName", "Title", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 6, 20, 22, 6, 35, 174, DateTimeKind.Local).AddTicks(3771), new DateTime(1980, 5, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), "EMP001", "أحمد", "الأحمد", "بغداد", "عقيد", "محمد", "STAT001", "علي", "الدكتور", new DateTime(2025, 6, 20, 22, 6, 35, 174, DateTimeKind.Local).AddTicks(4074) },
                    { 2, new DateTime(2025, 6, 20, 22, 6, 35, 174, DateTimeKind.Local).AddTicks(4365), new DateTime(1985, 8, 22, 0, 0, 0, 0, DateTimeKind.Unspecified), "EMP002", "فاطمة", "الزهراء", "البصرة", "مقدم", "حسن", "STAT002", "عبدالله", null, new DateTime(2025, 6, 20, 22, 6, 35, 174, DateTimeKind.Local).AddTicks(4366) },
                    { 3, new DateTime(2025, 6, 20, 22, 6, 35, 174, DateTimeKind.Local).AddTicks(4370), new DateTime(1990, 12, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "EMP003", "خالد", "المحمود", "الموصل", "رائد", "عبدالرحمن", "STAT003", "صالح", null, new DateTime(2025, 6, 20, 22, 6, 35, 174, DateTimeKind.Local).AddTicks(4370) }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Officers_EmployeeNumber",
                table: "Officers",
                column: "EmployeeNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Officers_Name",
                table: "Officers",
                columns: new[] { "FirstName", "LastName" });

            migrationBuilder.CreateIndex(
                name: "IX_Officers_StatisticalNumber",
                table: "Officers",
                column: "StatisticalNumber",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Officers");
        }
    }
}
