using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Directorates
{
    public class CreateModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public CreateModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Directorate Directorate { get; set; } = default!;

        public SelectList AgenciesList { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            await LoadAgenciesAsync();
            Directorate = new Directorate();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadAgenciesAsync();
                return Page();
            }

            try
            {
                _context.Directorates.Add(Directorate);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"تم إضافة المديرية '{Directorate.Name}' بنجاح";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "حدث خطأ أثناء حفظ البيانات: " + ex.Message);
                await LoadAgenciesAsync();
                return Page();
            }
        }

        private async Task LoadAgenciesAsync()
        {
            var agencies = await _context.Agencies
                .Where(a => a.IsActive)
                .OrderBy(a => a.Name)
                .ToListAsync();

            AgenciesList = new SelectList(agencies, "Id", "Name");
        }
    }
}
