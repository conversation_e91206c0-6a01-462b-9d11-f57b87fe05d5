using System.ComponentModel.DataAnnotations;

namespace OfficersManagement.Models
{
    public class Subdistrict
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الناحية مطلوب")]
        [Display(Name = "اسم الناحية")]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "القضاء مطلوب")]
        [Display(Name = "القضاء")]
        public int DistrictId { get; set; }

        [Display(Name = "الوصف")]
        [StringLength(200)]
        public string? Description { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual District District { get; set; } = null!;
        public virtual ICollection<Village> Villages { get; set; } = new List<Village>();
        public virtual ICollection<Officer> Officers { get; set; } = new List<Officer>();
    }
}
