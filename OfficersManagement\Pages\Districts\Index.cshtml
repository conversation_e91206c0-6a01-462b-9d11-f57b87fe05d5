@page
@model OfficersManagement.Pages.Districts.IndexModel
@{
    ViewData["Title"] = "إدارة الأقضية";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-map-marked-alt me-2"></i>
                            إدارة الأقضية
                        </h4>
                        <a asp-page="./Create" class="btn btn-light">
                            <i class="fas fa-plus me-1"></i>
                            إضافة قضاء جديد
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <!-- جدول الأقضية -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم القضاء</th>
                                    <th>المحافظة</th>
                                    <th>الوصف</th>
                                    <th>عدد النواحي</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var district in Model.Districts)
                                {
                                    <tr>
                                        <td>
                                            <strong>@district.Name</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@district.Province.Name</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">@(district.Description ?? "لا يوجد وصف")</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">@district.Subdistricts.Count</span>
                                        </td>
                                        <td>
                                            <small>@district.CreatedAt.ToString("yyyy/MM/dd")</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-page="./Details" asp-route-id="@district.Id"
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@district.Id"
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-page="./Delete" asp-route-id="@district.Id"
                                                   class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Districts.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أقضية</h5>
                            <p class="text-muted">قم بإضافة قضاء جديد للبدء</p>
                            <a asp-page="./Create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة قضاء جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // تأكيد الحذف
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('a[href*="/Delete"]');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (!confirm('هل أنت متأكد من حذف هذا القضاء؟')) {
                        e.preventDefault();
                    }
                });
            });
        });
    </script>
}
