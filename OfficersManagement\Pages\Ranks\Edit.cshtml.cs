using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Ranks
{
    public class EditModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public EditModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Rank Rank { get; set; } = default!;
        
        public int OfficersCount { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var rank = await _context.Ranks
                .Include(r => r.Officers)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (rank == null)
            {
                return NotFound();
            }
            
            Rank = rank;
            OfficersCount = rank.Officers.Count;
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                // إعادة تحميل عدد الضباط في حالة الخطأ
                var rankForCount = await _context.Ranks
                    .Include(r => r.Officers)
                    .FirstOrDefaultAsync(r => r.Id == Rank.Id);
                OfficersCount = rankForCount?.Officers.Count ?? 0;
                return Page();
            }

            // التحقق من عدم تكرار اسم الرتبة (باستثناء الرتبة الحالية)
            var existingRankByName = await _context.Ranks
                .FirstOrDefaultAsync(r => r.Name == Rank.Name && r.Id != Rank.Id);
            
            if (existingRankByName != null)
            {
                ModelState.AddModelError("Rank.Name", "اسم الرتبة موجود مسبقاً");
                var rankForCount = await _context.Ranks
                    .Include(r => r.Officers)
                    .FirstOrDefaultAsync(r => r.Id == Rank.Id);
                OfficersCount = rankForCount?.Officers.Count ?? 0;
                return Page();
            }

            // التحقق من عدم تكرار ترتيب الرتبة (باستثناء الرتبة الحالية)
            var existingRankByOrder = await _context.Ranks
                .FirstOrDefaultAsync(r => r.Order == Rank.Order && r.Id != Rank.Id);
            
            if (existingRankByOrder != null)
            {
                ModelState.AddModelError("Rank.Order", "ترتيب الرتبة موجود مسبقاً");
                var rankForCount = await _context.Ranks
                    .Include(r => r.Officers)
                    .FirstOrDefaultAsync(r => r.Id == Rank.Id);
                OfficersCount = rankForCount?.Officers.Count ?? 0;
                return Page();
            }

            Rank.UpdatedAt = DateTime.Now;

            _context.Attach(Rank).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!RankExists(Rank.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            TempData["SuccessMessage"] = "تم تحديث الرتبة بنجاح";
            return RedirectToPage("./Index");
        }

        private bool RankExists(int id)
        {
            return _context.Ranks.Any(e => e.Id == id);
        }
    }
}
