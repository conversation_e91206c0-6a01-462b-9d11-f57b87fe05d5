using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;

namespace OfficersManagement.Services
{
    public interface IEmployeeNumberService
    {
        Task<string> GenerateEmployeeNumberAsync();
        Task<string> GenerateStatisticalNumberAsync(string fullName);
    }

    public class EmployeeNumberService : IEmployeeNumberService
    {
        private readonly ApplicationDbContext _context;

        public EmployeeNumberService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<string> GenerateEmployeeNumberAsync()
        {
            var currentYear = DateTime.Now.Year;
            var prefix = $"MOI-{currentYear}-";

            // البحث عن آخر رقم موظف في السنة الحالية
            var lastEmployeeNumber = await _context.Officers
                .Where(o => o.EmployeeNumber.StartsWith(prefix))
                .OrderByDescending(o => o.EmployeeNumber)
                .Select(o => o.EmployeeNumber)
                .FirstOrDefaultAsync();

            int nextSequence = 1;

            if (!string.IsNullOrEmpty(lastEmployeeNumber))
            {
                // استخراج الرقم التسلسلي من آخر رقم موظف
                var sequencePart = lastEmployeeNumber.Substring(prefix.Length);
                if (int.TryParse(sequencePart, out int lastSequence))
                {
                    nextSequence = lastSequence + 1;
                }
            }

            // تنسيق الرقم التسلسلي بـ 6 أرقام
            return $"{prefix}{nextSequence:D6}";
        }

        public async Task<string> GenerateStatisticalNumberAsync(string fullName)
        {
            // تحويل الاسم الكامل إلى رقم إحصائي باستخدام hash
            var nameBytes = System.Text.Encoding.UTF8.GetBytes(fullName.Trim().ToLower());
            var hash = System.Security.Cryptography.SHA256.HashData(nameBytes);
            
            // أخذ أول 8 بايت وتحويلها إلى رقم
            var hashNumber = BitConverter.ToUInt64(hash, 0);
            var statisticalNumber = (hashNumber % 99999999 + 10000000).ToString();

            // التأكد من عدم تكرار الرقم الإحصائي
            var existingNumber = await _context.Officers
                .AnyAsync(o => o.StatisticalNumber == statisticalNumber);

            if (existingNumber)
            {
                // في حالة التكرار، إضافة رقم عشوائي
                var random = new Random();
                var suffix = random.Next(10, 99);
                statisticalNumber = statisticalNumber.Substring(0, 6) + suffix.ToString();
                
                // التحقق مرة أخرى
                while (await _context.Officers.AnyAsync(o => o.StatisticalNumber == statisticalNumber))
                {
                    suffix = random.Next(10, 99);
                    statisticalNumber = statisticalNumber.Substring(0, 6) + suffix.ToString();
                }
            }

            return statisticalNumber;
        }
    }
}
