using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;
using OfficersManagement.Services;

namespace OfficersManagement.Pages.Officers
{
    public class EditModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly IEmployeeNumberService _employeeNumberService;
        private readonly ImageCompressionService _imageCompressionService;

        public EditModel(ApplicationDbContext context, IEmployeeNumberService employeeNumberService, ImageCompressionService imageCompressionService)
        {
            _context = context;
            _employeeNumberService = employeeNumberService;
            _imageCompressionService = imageCompressionService;
        }

        [BindProperty]
        public Officer Officer { get; set; } = default!;

        public SelectList RanksList { get; set; } = default!;
        public SelectList ProvincesList { get; set; } = default!;
        public SelectList AgenciesList { get; set; } = default!;
        public SelectList DirectoratesList { get; set; } = default!;
        public SelectList DepartmentsList { get; set; } = default!;
        public SelectList DivisionsList { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var officer = await _context.Officers.FirstOrDefaultAsync(m => m.Id == id);

            if (officer == null)
            {
                return NotFound();
            }

            Officer = officer;
            await LoadRanksAndProvincesAsync();
            return Page();
        }

        private async Task LoadRanksAndProvincesAsync()
        {
            var ranks = await _context.Ranks
                .Where(r => r.IsActive)
                .OrderBy(r => r.Order)
                .ToListAsync();

            RanksList = new SelectList(ranks, "Id", "Name");

            var provinces = await _context.Provinces
                .OrderBy(p => p.Name)
                .ToListAsync();

            ProvincesList = new SelectList(provinces, "Id", "Name");

            var agencies = await _context.Agencies
                .Where(a => a.IsActive)
                .OrderBy(a => a.Name)
                .ToListAsync();

            AgenciesList = new SelectList(agencies, "Id", "Name");

            // تهيئة القوائم الفارغة
            DirectoratesList = new SelectList(new List<object>(), "Id", "Name");
            DepartmentsList = new SelectList(new List<object>(), "Id", "Name");
            DivisionsList = new SelectList(new List<object>(), "Id", "Name");
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadRanksAndProvincesAsync();
                return Page();
            }

            // التحقق من عدم تكرار الاسم الكامل (باستثناء الضابط الحالي)
            var existingOfficerByFullName = await _context.Officers
                .FirstOrDefaultAsync(o => o.FirstName == Officer.FirstName &&
                                         o.SecondName == Officer.SecondName &&
                                         o.ThirdName == Officer.ThirdName &&
                                         o.FourthName == Officer.FourthName &&
                                         o.Id != Officer.Id);

            if (existingOfficerByFullName != null)
            {
                ModelState.AddModelError("", "يوجد ضابط بنفس الاسم الكامل مسبقاً");
                await LoadRanksAndProvincesAsync();
                return Page();
            }

            // التحقق من عدم تكرار الرقم الإحصائي (باستثناء الضابط الحالي)
            var existingOfficerByStatisticalNumber = await _context.Officers
                .FirstOrDefaultAsync(o => o.StatisticalNumber == Officer.StatisticalNumber && o.Id != Officer.Id);

            if (existingOfficerByStatisticalNumber != null)
            {
                ModelState.AddModelError("Officer.StatisticalNumber", "يوجد ضابط بنفس الرقم الإحصائي مسبقاً");
                await LoadRanksAndProvincesAsync();
                return Page();
            }

            // التحقق من صحة تاريخ الولادة
            if (Officer.DateOfBirth > DateTime.Today)
            {
                ModelState.AddModelError("Officer.DateOfBirth", "تاريخ الولادة لا يمكن أن يكون في المستقبل");
                return Page();
            }

            // التحقق من العمر المنطقي (بين 18 و 65 سنة)
            var age = DateTime.Today.Year - Officer.DateOfBirth.Year;
            if (Officer.DateOfBirth.Date > DateTime.Today.AddYears(-age))
                age--;

            if (age < 18)
            {
                ModelState.AddModelError("Officer.DateOfBirth", "العمر يجب أن يكون 18 سنة على الأقل");
                return Page();
            }

            if (age > 65)
            {
                ModelState.AddModelError("Officer.DateOfBirth", "العمر يجب أن يكون أقل من 65 سنة");
                return Page();
            }

            // معالجة رفع الصورة
            await HandlePhotoUploadAsync();

            Officer.UpdatedAt = DateTime.Now;

            _context.Attach(Officer).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!OfficerExists(Officer.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            TempData["SuccessMessage"] = "تم تحديث بيانات الضابط بنجاح";
            return RedirectToPage("./Index");
        }

        private async Task HandlePhotoUploadAsync()
        {
            var photoData = Request.Form["PhotoData"].FirstOrDefault();
            var photoType = Request.Form["PhotoType"].FirstOrDefault();

            if (!string.IsNullOrEmpty(photoData) && !string.IsNullOrEmpty(photoType))
            {
                try
                {
                    if (photoType == "remove" && photoData == "REMOVE_PHOTO")
                    {
                        // إزالة الصورة
                        Officer.PhotoData = null;
                        Officer.PhotoContentType = null;
                        Console.WriteLine("Photo removed");
                    }
                    else if (photoType == "camera" || photoType == "file")
                    {
                        byte[] imageBytes = null;
                        string contentType = "image/jpeg";

                        // معالجة البيانات (base64) سواء من الكاميرا أو الملف
                        if (photoData.Contains(","))
                        {
                            var base64Data = photoData.Split(',')[1]; // إزالة البادئة data:image/...;base64,
                            imageBytes = Convert.FromBase64String(base64Data);

                            // تحديد نوع المحتوى من البادئة
                            var prefix = photoData.Split(',')[0];
                            if (prefix.Contains("image/png"))
                                contentType = "image/png";
                            else if (prefix.Contains("image/gif"))
                                contentType = "image/gif";
                            else
                                contentType = "image/jpeg";
                        }

                        // حفظ البيانات الثنائية للصورة مع الضغط
                        if (imageBytes != null && imageBytes.Length > 0)
                        {
                            Console.WriteLine($"حجم الصورة الأصلية: {_imageCompressionService.GetImageInfo(imageBytes)}");

                            // ضغط الصورة
                            var compressedBytes = _imageCompressionService.CompressImage(imageBytes, contentType);

                            Console.WriteLine($"حجم الصورة المضغوطة: {_imageCompressionService.GetImageInfo(compressedBytes)}");

                            Officer.PhotoData = compressedBytes;
                            Officer.PhotoContentType = contentType;
                            Console.WriteLine($"Photo saved: {compressedBytes.Length} bytes, type: {contentType}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    // في حالة فشل رفع الصورة، لا نوقف العملية
                    Console.WriteLine($"خطأ في معالجة الصورة: {ex.Message}");
                }
            }
        }

        private bool OfficerExists(int id)
        {
            return _context.Officers.Any(e => e.Id == id);
        }
    }
}
