using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.Generic;

namespace OfficersManagement.Models
{
    public class Officer
    {
        [Key]
        public int Id { get; set; }

        [Display(Name = "رقم الموظف")]
        [StringLength(20)]
        public string EmployeeNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرقم الإحصائي مطلوب")]
        [Display(Name = "الرقم الإحصائي")]
        [StringLength(20)]
        [RegularExpression(@"^\d+$", ErrorMessage = "الرقم الإحصائي يجب أن يحتوي على أرقام فقط")]
        public string StatisticalNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرتبة مطلوبة")]
        [Display(Name = "الرتبة")]
        public int RankId { get; set; }

        // Navigation property
        public virtual Rank? Rank { get; set; }

        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [Display(Name = "الاسم الأول")]
        [StringLength(50)]
        [RegularExpression(@"^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]+$", ErrorMessage = "الاسم الأول يجب أن يحتوي على أحرف عربية فقط")]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الثاني مطلوب")]
        [Display(Name = "الاسم الثاني")]
        [StringLength(50)]
        [RegularExpression(@"^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]+$", ErrorMessage = "الاسم الثاني يجب أن يحتوي على أحرف عربية فقط")]
        public string SecondName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الثالث مطلوب")]
        [Display(Name = "الاسم الثالث")]
        [StringLength(50)]
        [RegularExpression(@"^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]+$", ErrorMessage = "الاسم الثالث يجب أن يحتوي على أحرف عربية فقط")]
        public string ThirdName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الرابع مطلوب")]
        [Display(Name = "الاسم الرابع")]
        [StringLength(50)]
        [RegularExpression(@"^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]+$", ErrorMessage = "الاسم الرابع يجب أن يحتوي على أحرف عربية فقط")]
        public string FourthName { get; set; } = string.Empty;

        [Required(ErrorMessage = "اللقب مطلوب")]
        [Display(Name = "اللقب")]
        [StringLength(50)]
        [RegularExpression(@"^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]+$", ErrorMessage = "اللقب يجب أن يحتوي على أحرف عربية فقط")]
        public string Title { get; set; } = string.Empty;

        [Display(Name = "الصورة الشخصية")]
        public byte[]? PhotoData { get; set; }

        [Display(Name = "نوع الصورة")]
        [StringLength(50)]
        public string? PhotoContentType { get; set; }

        [Required(ErrorMessage = "محل الولادة مطلوب")]
        [Display(Name = "محل الولادة")]
        [StringLength(100)]
        public string PlaceOfBirth { get; set; } = string.Empty;

        [Required(ErrorMessage = "تاريخ الولادة مطلوب")]
        [Display(Name = "تاريخ الولادة")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [Required(ErrorMessage = "الحالة الاجتماعية مطلوبة")]
        [Display(Name = "الحالة الاجتماعية")]
        [StringLength(20)]
        public string MaritalStatus { get; set; } = string.Empty;

        [Required(ErrorMessage = "فصيلة الدم مطلوبة")]
        [Display(Name = "فصيلة الدم")]
        [StringLength(10)]
        public string BloodType { get; set; } = string.Empty;

        [Required(ErrorMessage = "الحالة الصحية مطلوبة")]
        [Display(Name = "الحالة الصحية")]
        [StringLength(50)]
        public string HealthStatus { get; set; } = string.Empty;

        [Display(Name = "محافظة السكن")]
        public int? ProvinceId { get; set; }

        [Display(Name = "القضاء")]
        [StringLength(100)]
        public string? District { get; set; }

        [Display(Name = "الناحية")]
        [StringLength(100)]
        public string? Subdistrict { get; set; }

        [Display(Name = "القرية")]
        [StringLength(100)]
        public string? Village { get; set; }

        [Display(Name = "الحي")]
        [StringLength(100)]
        public string? Neighborhood { get; set; }

        [Display(Name = "المحلة")]
        [StringLength(100)]
        public string? Locality { get; set; }

        [Display(Name = "الزقاق")]
        [StringLength(100)]
        public string? Alley { get; set; }

        [Display(Name = "رقم الدار")]
        [StringLength(50)]
        public string? HouseNumber { get; set; }

        [Display(Name = "أقرب نقطة دالة")]
        [StringLength(200)]
        public string? NearestLandmark { get; set; }



        // Navigation properties
        public virtual Province? Province { get; set; }

        [Display(Name = "الاسم الكامل")]
        [NotMapped]
        public string FullName
        {
            get
            {
                var nameParts = new List<string>();

                if (!string.IsNullOrWhiteSpace(FirstName)) nameParts.Add(FirstName);
                if (!string.IsNullOrWhiteSpace(SecondName)) nameParts.Add(SecondName);
                if (!string.IsNullOrWhiteSpace(ThirdName)) nameParts.Add(ThirdName);
                if (!string.IsNullOrWhiteSpace(FourthName)) nameParts.Add(FourthName);
                if (!string.IsNullOrWhiteSpace(Title)) nameParts.Add(Title);

                return nameParts.Count > 0 ? string.Join(" ", nameParts) : "غير محدد";
            }
        }




        [Display(Name = "العنوان بالتفصيل")]
        [NotMapped]
        public string DetailedAddress
        {
            get
            {
                var addressParts = new List<string>();

                // إضافة المحافظة
                if (Province != null && !string.IsNullOrWhiteSpace(Province.Name))
                    addressParts.Add($"محافظة {Province.Name}");

                // إضافة القضاء
                if (!string.IsNullOrWhiteSpace(District))
                    addressParts.Add($"قضاء {District}");

                // إضافة الناحية
                if (!string.IsNullOrWhiteSpace(Subdistrict))
                    addressParts.Add($"ناحية {Subdistrict}");

                // إضافة القرية
                if (!string.IsNullOrWhiteSpace(Village))
                    addressParts.Add($"قرية {Village}");

                // إضافة الحي
                if (!string.IsNullOrWhiteSpace(Neighborhood))
                    addressParts.Add($"حي {Neighborhood}");

                // إضافة المحلة
                if (!string.IsNullOrWhiteSpace(Locality))
                    addressParts.Add($"محلة {Locality}");

                // إضافة الزقاق
                if (!string.IsNullOrWhiteSpace(Alley))
                    addressParts.Add($"زقاق {Alley}");

                // إضافة رقم الدار
                if (!string.IsNullOrWhiteSpace(HouseNumber))
                    addressParts.Add($"دار رقم {HouseNumber}");

                // إضافة أقرب نقطة دالة
                if (!string.IsNullOrWhiteSpace(NearestLandmark))
                    addressParts.Add($"قرب {NearestLandmark}");

                return addressParts.Count > 0 ? string.Join(" - ", addressParts) : "غير محدد";
            }
        }



        [Display(Name = "العمر")]
        [NotMapped]
        public string Age
        {
            get
            {
                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Year;

                if (DateOfBirth.Date > today.AddYears(-age))
                    age--;

                var nextBirthday = DateOfBirth.AddYears(age + 1);
                var daysUntilBirthday = (nextBirthday - today).Days;

                var months = 0;
                var days = 0;

                if (today.Month < DateOfBirth.Month ||
                    (today.Month == DateOfBirth.Month && today.Day < DateOfBirth.Day))
                {
                    months = 12 - DateOfBirth.Month + today.Month;
                    if (today.Day >= DateOfBirth.Day)
                        months--;
                }
                else
                {
                    months = today.Month - DateOfBirth.Month;
                }

                if (today.Day >= DateOfBirth.Day)
                {
                    days = today.Day - DateOfBirth.Day;
                }
                else
                {
                    months--;
                    days = DateTime.DaysInMonth(today.Year, today.Month == 1 ? 12 : today.Month - 1) - DateOfBirth.Day + today.Day;
                }

                if (months < 0)
                {
                    months += 12;
                    age--;
                }

                return $"{age} سنة، {months} شهر، {days} يوم";
            }
        }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
