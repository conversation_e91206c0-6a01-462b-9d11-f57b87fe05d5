using System.ComponentModel.DataAnnotations;

namespace OfficersManagement.Models
{
    public class District
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم القضاء مطلوب")]
        [Display(Name = "اسم القضاء")]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "المحافظة مطلوبة")]
        [Display(Name = "المحافظة")]
        public int ProvinceId { get; set; }

        [Display(Name = "الوصف")]
        [StringLength(200)]
        public string? Description { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Province Province { get; set; } = null!;
        public virtual ICollection<Subdistrict> Subdistricts { get; set; } = new List<Subdistrict>();
        public virtual ICollection<Officer> Officers { get; set; } = new List<Officer>();
    }
}
