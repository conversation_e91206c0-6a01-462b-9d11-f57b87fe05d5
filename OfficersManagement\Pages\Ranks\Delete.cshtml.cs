using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Ranks
{
    public class DeleteModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public DeleteModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Rank Rank { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var rank = await _context.Ranks
                .Include(r => r.Officers)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (rank == null)
            {
                return NotFound();
            }
            
            Rank = rank;
            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var rank = await _context.Ranks
                .Include(r => r.Officers)
                .FirstOrDefaultAsync(r => r.Id == id);
                
            if (rank != null)
            {
                // التحقق من عدم وجود ضباط مرتبطين بهذه الرتبة
                if (rank.Officers.Count > 0)
                {
                    TempData["ErrorMessage"] = $"لا يمكن حذف الرتبة '{rank.Name}' لأنها مرتبطة بـ {rank.Officers.Count} ضابط";
                    return RedirectToPage("./Index");
                }

                Rank = rank;
                _context.Ranks.Remove(Rank);
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = $"تم حذف الرتبة '{rank.Name}' بنجاح";
            }

            return RedirectToPage("./Index");
        }
    }
}
