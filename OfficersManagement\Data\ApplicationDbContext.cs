using Microsoft.EntityFrameworkCore;
using OfficersManagement.Models;

namespace OfficersManagement.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Officer> Officers { get; set; }
        public DbSet<Rank> Ranks { get; set; }
        public DbSet<Province> Provinces { get; set; }
        public DbSet<District> Districts { get; set; }
        public DbSet<Subdistrict> Subdistricts { get; set; }
        public DbSet<Village> Villages { get; set; }
        public DbSet<Agency> Agencies { get; set; }
        public DbSet<Directorate> Directorates { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<Division> Divisions { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين نموذج Officer
            modelBuilder.Entity<Officer>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.EmployeeNumber)
                    .IsRequired()
                    .HasMaxLength(20);

                entity.Property(e => e.StatisticalNumber)
                    .IsRequired()
                    .HasMaxLength(20);

                entity.Property(e => e.RankId)
                    .IsRequired();

                // Foreign key relationship
                entity.HasOne(e => e.Rank)
                    .WithMany(r => r.Officers)
                    .HasForeignKey(e => e.RankId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.Property(e => e.FirstName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.SecondName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.ThirdName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.FourthName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Title)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.PlaceOfBirth)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.DateOfBirth)
                    .IsRequired();

                entity.Property(e => e.CreatedAt)
                    .IsRequired();

                entity.Property(e => e.UpdatedAt)
                    .IsRequired();

                // Photo configuration
                entity.Property(e => e.PhotoData)
                    .HasColumnType("varbinary(max)");

                entity.Property(e => e.PhotoContentType)
                    .HasMaxLength(50);

                // إنشاء فهارس للبحث السريع
                entity.HasIndex(e => e.EmployeeNumber)
                    .IsUnique()
                    .HasDatabaseName("IX_Officers_EmployeeNumber");

                entity.HasIndex(e => e.StatisticalNumber)
                    .IsUnique()
                    .HasDatabaseName("IX_Officers_StatisticalNumber");

                entity.HasIndex(e => new { e.FirstName, e.FourthName })
                    .HasDatabaseName("IX_Officers_Name");

                // فهرس للاسم الكامل (محسوب)
                entity.HasIndex(e => new { e.FirstName, e.SecondName, e.ThirdName, e.FourthName })
                    .IsUnique()
                    .HasDatabaseName("IX_Officers_FullName");
            });

            // تكوين نموذج Rank
            modelBuilder.Entity<Rank>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Description)
                    .HasMaxLength(200);

                entity.Property(e => e.Order)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .IsRequired();

                entity.Property(e => e.CreatedAt)
                    .IsRequired();

                entity.Property(e => e.UpdatedAt)
                    .IsRequired();

                // فهرس فريد لاسم الرتبة
                entity.HasIndex(e => e.Name)
                    .IsUnique()
                    .HasDatabaseName("IX_Ranks_Name");

                // فهرس لترتيب الرتبة
                entity.HasIndex(e => e.Order)
                    .HasDatabaseName("IX_Ranks_Order");
            });

            // إضافة بيانات تجريبية للرتب
            modelBuilder.Entity<Rank>().HasData(
                new Rank { Id = 1, Name = "فريق أول", Description = "أعلى رتبة عسكرية", Order = 1, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 2, Name = "فريق", Description = "رتبة عسكرية عليا", Order = 2, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 3, Name = "لواء", Description = "رتبة عسكرية عليا", Order = 3, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 4, Name = "عميد", Description = "رتبة عسكرية عليا", Order = 4, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 5, Name = "عقيد", Description = "رتبة ضباط كبار", Order = 5, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 6, Name = "مقدم", Description = "رتبة ضباط كبار", Order = 6, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 7, Name = "رائد", Description = "رتبة ضباط", Order = 7, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 8, Name = "نقيب", Description = "رتبة ضباط", Order = 8, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 9, Name = "ملازم أول", Description = "رتبة ضباط صغار", Order = 9, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 10, Name = "ملازم", Description = "رتبة ضباط صغار", Order = 10, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 11, Name = "رقيب أول", Description = "رتبة ضباط صف", Order = 11, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 12, Name = "رقيب", Description = "رتبة ضباط صف", Order = 12, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 13, Name = "عريف", Description = "رتبة أفراد", Order = 13, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 14, Name = "جندي أول", Description = "رتبة أفراد", Order = 14, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 15, Name = "جندي", Description = "أدنى رتبة عسكرية", Order = 15, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) }
            );

            // إضافة بيانات تجريبية للضباط
            modelBuilder.Entity<Officer>().HasData(
                new Officer
                {
                    Id = 1,
                    EmployeeNumber = "EMP001",
                    StatisticalNumber = "STAT001",
                    RankId = 5, // عقيد
                    FirstName = "أحمد",
                    SecondName = "محمد",
                    ThirdName = "علي",
                    FourthName = "الأحمد",
                    Title = "الدكتور",
                    PlaceOfBirth = "بغداد",
                    DateOfBirth = new DateTime(1980, 5, 15),
                    CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0),
                    UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0)
                },
                new Officer
                {
                    Id = 2,
                    EmployeeNumber = "EMP002",
                    StatisticalNumber = "STAT002",
                    RankId = 6, // مقدم
                    FirstName = "فاطمة",
                    SecondName = "حسن",
                    ThirdName = "عبدالله",
                    FourthName = "الزهراء",
                    Title = "المهندسة",
                    PlaceOfBirth = "البصرة",
                    DateOfBirth = new DateTime(1985, 8, 22),
                    CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0),
                    UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0)
                },
                new Officer
                {
                    Id = 3,
                    EmployeeNumber = "EMP003",
                    StatisticalNumber = "STAT003",
                    RankId = 7, // رائد
                    FirstName = "خالد",
                    SecondName = "عبدالرحمن",
                    ThirdName = "صالح",
                    FourthName = "المحمود",
                    Title = "الأستاذ",
                    PlaceOfBirth = "الموصل",
                    DateOfBirth = new DateTime(1990, 12, 10),
                    CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0),
                    UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0)
                }
            );

            // تكوين نموذج Agency
            modelBuilder.Entity<Agency>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.HasIndex(e => e.Name).IsUnique();
            });

            // تكوين نموذج Directorate
            modelBuilder.Entity<Directorate>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.HasOne(e => e.Agency)
                    .WithMany(a => a.Directorates)
                    .HasForeignKey(e => e.AgencyId)
                    .OnDelete(DeleteBehavior.Restrict);
                entity.HasIndex(e => new { e.Name, e.AgencyId }).IsUnique();
            });

            // تكوين نموذج Department
            modelBuilder.Entity<Department>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.HasOne(e => e.Directorate)
                    .WithMany(d => d.Departments)
                    .HasForeignKey(e => e.DirectorateId)
                    .OnDelete(DeleteBehavior.Restrict);
                entity.HasIndex(e => new { e.Name, e.DirectorateId }).IsUnique();
            });

            // تكوين نموذج Division
            modelBuilder.Entity<Division>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.HasOne(e => e.Department)
                    .WithMany(d => d.Divisions)
                    .HasForeignKey(e => e.DepartmentId)
                    .OnDelete(DeleteBehavior.Restrict);
                entity.HasIndex(e => new { e.Name, e.DepartmentId }).IsUnique();
            });

            // بيانات تجريبية للوكالات
            modelBuilder.Entity<Agency>().HasData(
                new Agency { Id = 1, Name = "وكالة وزارة الداخلية", Description = "وكالة وزارة الداخلية للشؤون الأمنية", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Agency { Id = 2, Name = "وكالة وزارة الدفاع", Description = "وكالة وزارة الدفاع للشؤون العسكرية", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Agency { Id = 3, Name = "وكالة الاستخبارات", Description = "وكالة الاستخبارات العراقية", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) }
            );

            // بيانات تجريبية للمديريات
            modelBuilder.Entity<Directorate>().HasData(
                // مديريات وزارة الداخلية
                new Directorate { Id = 1, Name = "مديرية الشرطة", Description = "مديرية عامة للشرطة", AgencyId = 1, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Directorate { Id = 2, Name = "مديرية الأمن العام", Description = "مديرية عامة للأمن العام", AgencyId = 1, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Directorate { Id = 3, Name = "مديرية الجوازات", Description = "مديرية عامة للجوازات", AgencyId = 1, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                // مديريات وزارة الدفاع
                new Directorate { Id = 4, Name = "مديرية العمليات", Description = "مديرية عامة للعمليات", AgencyId = 2, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Directorate { Id = 5, Name = "مديرية الاستخبارات العسكرية", Description = "مديرية عامة للاستخبارات العسكرية", AgencyId = 2, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                // مديريات وكالة الاستخبارات
                new Directorate { Id = 6, Name = "مديرية مكافحة الإرهاب", Description = "مديرية عامة لمكافحة الإرهاب", AgencyId = 3, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) }
            );

            // بيانات تجريبية للأقسام
            modelBuilder.Entity<Department>().HasData(
                // أقسام مديرية الشرطة
                new Department { Id = 1, Name = "قسم شرطة بغداد", Description = "قسم شرطة محافظة بغداد", DirectorateId = 1, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Department { Id = 2, Name = "قسم شرطة البصرة", Description = "قسم شرطة محافظة البصرة", DirectorateId = 1, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Department { Id = 3, Name = "قسم شرطة الموصل", Description = "قسم شرطة محافظة نينوى", DirectorateId = 1, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                // أقسام مديرية الأمن العام
                new Department { Id = 4, Name = "قسم أمن بغداد", Description = "قسم أمن محافظة بغداد", DirectorateId = 2, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Department { Id = 5, Name = "قسم أمن البصرة", Description = "قسم أمن محافظة البصرة", DirectorateId = 2, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) }
            );

            // بيانات تجريبية للشعب
            modelBuilder.Entity<Division>().HasData(
                // شعب قسم شرطة بغداد
                new Division { Id = 1, Name = "شعبة التحقيقات", Description = "شعبة التحقيقات الجنائية", DepartmentId = 1, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Division { Id = 2, Name = "شعبة الدوريات", Description = "شعبة الدوريات والمراقبة", DepartmentId = 1, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Division { Id = 3, Name = "شعبة الطوارئ", Description = "شعبة الطوارئ والأزمات", DepartmentId = 1, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                // شعب قسم أمن بغداد
                new Division { Id = 4, Name = "شعبة المعلومات", Description = "شعبة جمع وتحليل المعلومات", DepartmentId = 4, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Division { Id = 5, Name = "شعبة المتابعة", Description = "شعبة متابعة العناصر المشبوهة", DepartmentId = 4, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0) }
            );
        }
    }
}
