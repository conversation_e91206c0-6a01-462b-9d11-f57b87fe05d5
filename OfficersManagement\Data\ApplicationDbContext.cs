using Microsoft.EntityFrameworkCore;
using OfficersManagement.Models;

namespace OfficersManagement.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Officer> Officers { get; set; }
        public DbSet<Rank> Ranks { get; set; }
        public DbSet<Province> Provinces { get; set; }
        public DbSet<District> Districts { get; set; }
        public DbSet<Subdistrict> Subdistricts { get; set; }
        public DbSet<Village> Villages { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين نموذج Officer
            modelBuilder.Entity<Officer>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.EmployeeNumber)
                    .IsRequired()
                    .HasMaxLength(20);

                entity.Property(e => e.StatisticalNumber)
                    .IsRequired()
                    .HasMaxLength(20);

                entity.Property(e => e.RankId)
                    .IsRequired();

                // Foreign key relationship
                entity.HasOne(e => e.Rank)
                    .WithMany(r => r.Officers)
                    .HasForeignKey(e => e.RankId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.Property(e => e.FirstName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.SecondName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.ThirdName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.FourthName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Title)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.PlaceOfBirth)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.DateOfBirth)
                    .IsRequired();

                entity.Property(e => e.CreatedAt)
                    .IsRequired();

                entity.Property(e => e.UpdatedAt)
                    .IsRequired();

                // Photo configuration
                entity.Property(e => e.PhotoData)
                    .HasColumnType("varbinary(max)");

                entity.Property(e => e.PhotoContentType)
                    .HasMaxLength(50);

                // إنشاء فهارس للبحث السريع
                entity.HasIndex(e => e.EmployeeNumber)
                    .IsUnique()
                    .HasDatabaseName("IX_Officers_EmployeeNumber");

                entity.HasIndex(e => e.StatisticalNumber)
                    .IsUnique()
                    .HasDatabaseName("IX_Officers_StatisticalNumber");

                entity.HasIndex(e => new { e.FirstName, e.FourthName })
                    .HasDatabaseName("IX_Officers_Name");

                // فهرس للاسم الكامل (محسوب)
                entity.HasIndex(e => new { e.FirstName, e.SecondName, e.ThirdName, e.FourthName })
                    .IsUnique()
                    .HasDatabaseName("IX_Officers_FullName");
            });

            // تكوين نموذج Rank
            modelBuilder.Entity<Rank>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Description)
                    .HasMaxLength(200);

                entity.Property(e => e.Order)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .IsRequired();

                entity.Property(e => e.CreatedAt)
                    .IsRequired();

                entity.Property(e => e.UpdatedAt)
                    .IsRequired();

                // فهرس فريد لاسم الرتبة
                entity.HasIndex(e => e.Name)
                    .IsUnique()
                    .HasDatabaseName("IX_Ranks_Name");

                // فهرس لترتيب الرتبة
                entity.HasIndex(e => e.Order)
                    .HasDatabaseName("IX_Ranks_Order");
            });

            // إضافة بيانات تجريبية للرتب
            modelBuilder.Entity<Rank>().HasData(
                new Rank { Id = 1, Name = "فريق أول", Description = "أعلى رتبة عسكرية", Order = 1, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 2, Name = "فريق", Description = "رتبة عسكرية عليا", Order = 2, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 3, Name = "لواء", Description = "رتبة عسكرية عليا", Order = 3, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 4, Name = "عميد", Description = "رتبة عسكرية عليا", Order = 4, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 5, Name = "عقيد", Description = "رتبة ضباط كبار", Order = 5, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 6, Name = "مقدم", Description = "رتبة ضباط كبار", Order = 6, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 7, Name = "رائد", Description = "رتبة ضباط", Order = 7, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 8, Name = "نقيب", Description = "رتبة ضباط", Order = 8, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 9, Name = "ملازم أول", Description = "رتبة ضباط صغار", Order = 9, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 10, Name = "ملازم", Description = "رتبة ضباط صغار", Order = 10, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 11, Name = "رقيب أول", Description = "رتبة ضباط صف", Order = 11, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 12, Name = "رقيب", Description = "رتبة ضباط صف", Order = 12, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 13, Name = "عريف", Description = "رتبة أفراد", Order = 13, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 14, Name = "جندي أول", Description = "رتبة أفراد", Order = 14, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) },
                new Rank { Id = 15, Name = "جندي", Description = "أدنى رتبة عسكرية", Order = 15, IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0), UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0) }
            );

            // إضافة بيانات تجريبية للضباط
            modelBuilder.Entity<Officer>().HasData(
                new Officer
                {
                    Id = 1,
                    EmployeeNumber = "EMP001",
                    StatisticalNumber = "STAT001",
                    RankId = 5, // عقيد
                    FirstName = "أحمد",
                    SecondName = "محمد",
                    ThirdName = "علي",
                    FourthName = "الأحمد",
                    Title = "الدكتور",
                    PlaceOfBirth = "بغداد",
                    DateOfBirth = new DateTime(1980, 5, 15),
                    CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0),
                    UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0)
                },
                new Officer
                {
                    Id = 2,
                    EmployeeNumber = "EMP002",
                    StatisticalNumber = "STAT002",
                    RankId = 6, // مقدم
                    FirstName = "فاطمة",
                    SecondName = "حسن",
                    ThirdName = "عبدالله",
                    FourthName = "الزهراء",
                    Title = "المهندسة",
                    PlaceOfBirth = "البصرة",
                    DateOfBirth = new DateTime(1985, 8, 22),
                    CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0),
                    UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0)
                },
                new Officer
                {
                    Id = 3,
                    EmployeeNumber = "EMP003",
                    StatisticalNumber = "STAT003",
                    RankId = 7, // رائد
                    FirstName = "خالد",
                    SecondName = "عبدالرحمن",
                    ThirdName = "صالح",
                    FourthName = "المحمود",
                    Title = "الأستاذ",
                    PlaceOfBirth = "الموصل",
                    DateOfBirth = new DateTime(1990, 12, 10),
                    CreatedAt = new DateTime(2025, 1, 1, 10, 0, 0),
                    UpdatedAt = new DateTime(2025, 1, 1, 10, 0, 0)
                }
            );
        }
    }
}
