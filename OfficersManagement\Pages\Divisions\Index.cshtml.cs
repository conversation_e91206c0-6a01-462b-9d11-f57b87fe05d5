using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Divisions
{
    public class IndexModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public IndexModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<Division> Divisions { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Divisions = await _context.Divisions
                .Include(d => d.Department)
                    .ThenInclude(dept => dept!.Directorate)
                        .ThenInclude(dir => dir!.Agency)
                .OrderBy(d => d.Department!.Directorate!.Agency!.Name)
                .ThenBy(d => d.Department!.Directorate!.Name)
                .ThenBy(d => d.Department!.Name)
                .ThenBy(d => d.Name)
                .ToListAsync();
        }
    }
}
