@page
@model OfficersManagement.Pages.Directorates.IndexModel
@{
    ViewData["Title"] = "إدارة المديريات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-sitemap me-2"></i>
                    إدارة المديريات
                </h2>
                <a asp-page="./Create" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مديرية جديدة
                </a>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المديريات
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Directorates.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>اسم المديرية</th>
                                        <th>الوكالة</th>
                                        <th>الوصف</th>
                                        <th>عدد الأقسام</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var directorate in Model.Directorates)
                                    {
                                        <tr>
                                            <td>@directorate.Id</td>
                                            <td>
                                                <strong>@directorate.Name</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">@directorate.Agency?.Name</span>
                                            </td>
                                            <td>@directorate.Description</td>
                                            <td>
                                                <span class="badge bg-info">@directorate.Departments.Count</span>
                                            </td>
                                            <td>
                                                @if (directorate.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="./Edit" asp-route-id="@directorate.Id" class="btn btn-warning btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-page="./Delete" asp-route-id="@directorate.Id" class="btn btn-danger btn-sm">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مديريات مسجلة</h5>
                            <p class="text-muted">يمكنك إضافة مديرية جديدة من خلال الزر أعلاه</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
