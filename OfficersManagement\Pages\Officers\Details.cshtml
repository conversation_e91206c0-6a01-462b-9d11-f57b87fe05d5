@page
@model OfficersManagement.Pages.Officers.DetailsModel
@{
    ViewData["Title"] = "تفاصيل الضابط";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        تفاصيل الضابط
                    </h4>
                </div>
                <div class="card-body">
                    @if (Model.Officer == null)
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الضابط غير موجود
                        </div>
                    }
                    else
                    {
                        <!-- الصورة الشخصية -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">الصورة الشخصية</h6>
                                    </div>
                                    <div class="card-body text-center">
                                        @if (Model.Officer.PhotoData != null && Model.Officer.PhotoData.Length > 0)
                                        {
                                            <img src="data:@Model.Officer.PhotoContentType;base64,@Convert.ToBase64String(Model.Officer.PhotoData)"
                                                 alt="صورة @Model.Officer.FullName"
                                                 class="img-fluid rounded shadow"
                                                 style="max-width: 300px; max-height: 400px; object-fit: cover; cursor: pointer;"
                                                 onclick="openImageModal(this.src)" />
                                        }
                                        else
                                        {
                                            <div class="text-muted">
                                                <i class="fas fa-user-circle fa-5x"></i>
                                                <p class="mt-2">لا توجد صورة</p>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- البيانات الأساسية -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">المعلومات الأساسية</h6>
                                    </div>
                                    <div class="card-body">
                                        <dl class="row">
                                            <dt class="col-sm-5">رقم الموظف:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge bg-secondary">@Model.Officer.EmployeeNumber</span>
                                            </dd>
                                            <dt class="col-sm-5">الرقم الإحصائي:</dt>
                                            <dd class="col-sm-7">@Model.Officer.StatisticalNumber</dd>
                                            <dt class="col-sm-5">الرتبة:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge bg-success">@(Model.Officer.Rank?.Name ?? "غير محدد")</span>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">المعلومات الشخصية</h6>
                                    </div>
                                    <div class="card-body">
                                        <dl class="row">
                                            <dt class="col-sm-5">محل الولادة:</dt>
                                            <dd class="col-sm-7">@Model.Officer.PlaceOfBirth</dd>
                                            <dt class="col-sm-5">تاريخ الولادة:</dt>
                                            <dd class="col-sm-7">@Model.Officer.DateOfBirth.ToString("yyyy/MM/dd")</dd>
                                            <dt class="col-sm-5">العمر:</dt>
                                            <dd class="col-sm-7">
                                                <span class="text-info">@Model.Officer.Age</span>
                                            </dd>
                                            <dt class="col-sm-5">الحالة الاجتماعية:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge bg-primary">@Model.Officer.MaritalStatus</span>
                                            </dd>
                                            <dt class="col-sm-5">فصيلة الدم:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge bg-danger">@Model.Officer.BloodType</span>
                                            </dd>
                                            <dt class="col-sm-5">الحالة الصحية:</dt>
                                            <dd class="col-sm-7">
                                                <span class="badge @(Model.Officer.HealthStatus == "سليم" ? "bg-success" : Model.Officer.HealthStatus == "مريض" ? "bg-warning" : "bg-secondary")">@Model.Officer.HealthStatus</span>
                                            </dd>

                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل الاسم -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">تفاصيل الاسم</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <dt>اللقب:</dt>
                                                <dd>@Model.Officer.Title</dd>
                                            </div>
                                            <div class="col-md-2">
                                                <dt>الاسم الأول:</dt>
                                                <dd>@Model.Officer.FirstName</dd>
                                            </div>
                                            <div class="col-md-2">
                                                <dt>الاسم الثاني:</dt>
                                                <dd>@Model.Officer.SecondName</dd>
                                            </div>
                                            <div class="col-md-2">
                                                <dt>الاسم الثالث:</dt>
                                                <dd>@Model.Officer.ThirdName</dd>
                                            </div>
                                            <div class="col-md-2">
                                                <dt>الاسم الرابع:</dt>
                                                <dd>@Model.Officer.FourthName</dd>
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <dt>الاسم الكامل:</dt>
                                                <dd><h5 class="text-primary">@Model.Officer.FullName</h5></dd>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات السكن -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card mb-3">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-map-marker-alt me-2"></i>
                                            معلومات السكن
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <dt>المحافظة:</dt>
                                                <dd>@(Model.Officer.Province?.Name ?? "غير محدد")</dd>
                                            </div>
                                            <div class="col-md-3">
                                                <dt>القضاء:</dt>
                                                <dd>@(Model.Officer.District ?? "غير محدد")</dd>
                                            </div>
                                            <div class="col-md-3">
                                                <dt>الناحية:</dt>
                                                <dd>@(Model.Officer.Subdistrict ?? "غير محدد")</dd>
                                            </div>
                                            <div class="col-md-3">
                                                <dt>القرية:</dt>
                                                <dd>@(Model.Officer.Village ?? "غير محدد")</dd>
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-3">
                                                <dt>الحي:</dt>
                                                <dd>@(Model.Officer.Neighborhood ?? "غير محدد")</dd>
                                            </div>
                                            <div class="col-md-3">
                                                <dt>المحلة:</dt>
                                                <dd>@(Model.Officer.Locality ?? "غير محدد")</dd>
                                            </div>
                                            <div class="col-md-3">
                                                <dt>الزقاق:</dt>
                                                <dd>@(Model.Officer.Alley ?? "غير محدد")</dd>
                                            </div>
                                            <div class="col-md-3">
                                                <dt>رقم الدار:</dt>
                                                <dd>@(Model.Officer.HouseNumber ?? "غير محدد")</dd>
                                            </div>
                                        </div>
                                        @if (!string.IsNullOrEmpty(Model.Officer.NearestLandmark))
                                        {
                                            <div class="row mt-3">
                                                <div class="col-12">
                                                    <dt>أقرب نقطة دالة:</dt>
                                                    <dd class="text-muted">@Model.Officer.NearestLandmark</dd>
                                                </div>
                                            </div>
                                        }

                                        <!-- العنوان بالتفصيل -->
                                        <div class="row mt-4">
                                            <div class="col-12">
                                                <div class="alert alert-info">
                                                    <h6 class="alert-heading">
                                                        <i class="fas fa-map-marked-alt me-2"></i>
                                                        العنوان بالتفصيل
                                                    </h6>
                                                    <p class="mb-0 fw-bold">@Model.Officer.DetailedAddress</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات النظام -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">معلومات النظام</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <dt>تاريخ الإنشاء:</dt>
                                                <dd>@Model.Officer.CreatedAt.ToString("yyyy/MM/dd HH:mm")</dd>
                                            </div>
                                            <div class="col-md-6">
                                                <dt>تاريخ آخر تحديث:</dt>
                                                <dd>@Model.Officer.UpdatedAt.ToString("yyyy/MM/dd HH:mm")</dd>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-page="./Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للقائمة
                                    </a>
                                    <div>
                                        <a asp-page="./Edit" asp-route-id="@Model.Officer.Id" class="btn btn-warning me-2">
                                            <i class="fas fa-edit me-1"></i>
                                            تعديل
                                        </a>
                                        <a asp-page="./Delete" asp-route-id="@Model.Officer.Id" class="btn btn-danger">
                                            <i class="fas fa-trash me-1"></i>
                                            حذف
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">صورة الضابط</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="صورة الضابط" class="img-fluid" />
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function openImageModal(imageSrc) {
            document.getElementById('modalImage').src = imageSrc;
            var imageModal = new bootstrap.Modal(document.getElementById('imageModal'));
            imageModal.show();
        }
    </script>
}
