using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Officers
{
    public class DeleteModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public DeleteModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Officer Officer { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var officer = await _context.Officers.Include(o => o.Rank).FirstOrDefaultAsync(m => m.Id == id);

            if (officer == null)
            {
                return NotFound();
            }
            
            Officer = officer;
            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var officer = await _context.Officers.FindAsync(id);
            if (officer != null)
            {
                Officer = officer;
                _context.Officers.Remove(Officer);
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = "تم حذف الضابط بنجاح";
            }

            return RedirectToPage("./Index");
        }
    }
}
