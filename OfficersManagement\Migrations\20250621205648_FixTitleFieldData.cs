﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OfficersManagement.Migrations
{
    /// <inheritdoc />
    public partial class FixTitleFieldData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // تحديث أي بيانات تحتوي على "محدد" في حقل اللقب لتصبح فارغة
            migrationBuilder.Sql("UPDATE Officers SET Title = '' WHERE Title = 'محدد'");

            // تحديث أي بيانات تحتوي على "غير محدد" في حقل اللقب لتصبح فارغة
            migrationBuilder.Sql("UPDATE Officers SET Title = '' WHERE Title = 'غير محدد'");

            // تحديث حقل الحالة الصحية للبيانات الموجودة
            migrationBuilder.Sql("UPDATE Officers SET HealthStatus = 'سليم' WHERE HealthStatus = '' OR HealthStatus IS NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
