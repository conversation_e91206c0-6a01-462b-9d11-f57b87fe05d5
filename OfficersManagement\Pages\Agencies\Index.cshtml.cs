using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Agencies
{
    public class IndexModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public IndexModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<Agency> Agencies { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Agencies = await _context.Agencies
                .Include(a => a.Directorates)
                .OrderBy(a => a.Name)
                .ToListAsync();
        }
    }
}
