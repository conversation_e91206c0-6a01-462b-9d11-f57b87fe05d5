using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;

namespace OfficersManagement.Pages;

public class IndexModel : PageModel
{
    private readonly ILogger<IndexModel> _logger;
    private readonly ApplicationDbContext _context;

    public IndexModel(ILogger<IndexModel> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public int TotalOfficers { get; set; }
    public int OfficersAddedToday { get; set; }
    public int OfficersAddedThisMonth { get; set; }
    public string AverageAge { get; set; } = "0";

    public async Task OnGetAsync()
    {
        try
        {
            TotalOfficers = await _context.Officers.CountAsync();

            var today = DateTime.Today;
            OfficersAddedToday = await _context.Officers
                .CountAsync(o => o.CreatedAt.Date == today);

            var firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
            OfficersAddedThisMonth = await _context.Officers
                .CountAsync(o => o.CreatedAt >= firstDayOfMonth);

            if (TotalOfficers > 0)
            {
                var officers = await _context.Officers.ToListAsync();
                var totalAgeInYears = officers.Sum(o =>
                {
                    var age = DateTime.Today.Year - o.DateOfBirth.Year;
                    if (o.DateOfBirth.Date > DateTime.Today.AddYears(-age))
                        age--;
                    return age;
                });

                var avgAge = totalAgeInYears / (double)TotalOfficers;
                AverageAge = $"{avgAge:F1} سنة";
            }


        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard statistics");
            TotalOfficers = 0;
            OfficersAddedToday = 0;
            OfficersAddedThisMonth = 0;
            AverageAge = "غير متاح";
        }
    }
}
