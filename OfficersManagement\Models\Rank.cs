using System.ComponentModel.DataAnnotations;

namespace OfficersManagement.Models
{
    public class Rank
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الرتبة مطلوب")]
        [Display(Name = "اسم الرتبة")]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        [StringLength(200)]
        public string? Description { get; set; }

        [Required(ErrorMessage = "ترتيب الرتبة مطلوب")]
        [Display(Name = "ترتيب الرتبة")]
        [Range(1, 100, ErrorMessage = "ترتيب الرتبة يجب أن يكون بين 1 و 100")]
        public int Order { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation property
        public virtual ICollection<Officer> Officers { get; set; } = new List<Officer>();
    }
}
