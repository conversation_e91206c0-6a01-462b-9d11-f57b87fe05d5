using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Departments
{
    public class IndexModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public IndexModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<Department> Departments { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Departments = await _context.Departments
                .Include(d => d.Directorate)
                    .ThenInclude(dir => dir!.Agency)
                .Include(d => d.Divisions)
                .OrderBy(d => d.Directorate!.Agency!.Name)
                .ThenBy(d => d.Directorate!.Name)
                .ThenBy(d => d.Name)
                .ToListAsync();
        }
    }
}
