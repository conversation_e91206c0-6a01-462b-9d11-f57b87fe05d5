using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;

namespace OfficersManagement.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PhotoController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public PhotoController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet("{officerId}")]
        public async Task<IActionResult> GetPhoto(int officerId)
        {
            var officer = await _context.Officers
                .Where(o => o.Id == officerId)
                .Select(o => new { o.PhotoData, o.PhotoContentType })
                .FirstOrDefaultAsync();

            if (officer?.PhotoData == null || officer.PhotoData.Length == 0)
            {
                return NotFound();
            }

            return File(officer.PhotoData, officer.PhotoContentType ?? "image/jpeg");
        }
    }
}
