@page
@model OfficersManagement.Pages.Agencies.IndexModel
@{
    ViewData["Title"] = "إدارة الوكالات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-building me-2"></i>
                    إدارة الوكالات
                </h2>
                <a asp-page="./Create" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    إضافة وكالة جديدة
                </a>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الوكالات
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Agencies.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>اسم الوكالة</th>
                                        <th>الوصف</th>
                                        <th>عدد المديريات</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var agency in Model.Agencies)
                                    {
                                        <tr>
                                            <td>@agency.Id</td>
                                            <td>
                                                <strong>@agency.Name</strong>
                                            </td>
                                            <td>@agency.Description</td>
                                            <td>
                                                <span class="badge bg-info">@agency.Directorates.Count</span>
                                            </td>
                                            <td>
                                                @if (agency.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                            </td>
                                            <td>@agency.CreatedAt.ToString("yyyy/MM/dd")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="./Details" asp-route-id="@agency.Id" class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-page="./Edit" asp-route-id="@agency.Id" class="btn btn-warning btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-page="./Delete" asp-route-id="@agency.Id" class="btn btn-danger btn-sm">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد وكالات مسجلة</h5>
                            <p class="text-muted">يمكنك إضافة وكالة جديدة من خلال الزر أعلاه</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
