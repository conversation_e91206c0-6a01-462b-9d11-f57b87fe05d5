@page
@model OfficersManagement.Pages.Directorates.EditModel
@{
    ViewData["Title"] = "تعديل المديرية";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-edit me-2"></i>
                    تعديل المديرية
                </h2>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل بيانات المديرية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        <input type="hidden" asp-for="Directorate.Id" />
                        <input type="hidden" asp-for="Directorate.CreatedAt" />
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Directorate.AgencyId" class="form-label">الوكالة</label>
                                <select asp-for="Directorate.AgencyId" asp-items="Model.AgenciesList" class="form-select">
                                    <option value="">اختر الوكالة</option>
                                </select>
                                <span asp-validation-for="Directorate.AgencyId" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Directorate.Name" class="form-label">اسم المديرية</label>
                                <input asp-for="Directorate.Name" class="form-control" placeholder="أدخل اسم المديرية" />
                                <span asp-validation-for="Directorate.Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-10 mb-3">
                                <label asp-for="Directorate.Description" class="form-label">الوصف</label>
                                <textarea asp-for="Directorate.Description" class="form-control" rows="3" placeholder="أدخل وصف المديرية (اختياري)"></textarea>
                                <span asp-validation-for="Directorate.Description" class="text-danger"></span>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="form-check mt-4">
                                    <input asp-for="Directorate.IsActive" class="form-check-input" />
                                    <label asp-for="Directorate.IsActive" class="form-check-label">
                                        نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
