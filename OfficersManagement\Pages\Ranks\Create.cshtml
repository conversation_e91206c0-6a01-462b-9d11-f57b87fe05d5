@page
@model OfficersManagement.Pages.Ranks.CreateModel
@{
    ViewData["Title"] = "إضافة رتبة جديدة";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        إضافة رتبة جديدة
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <!-- اسم الرتبة -->
                            <div class="col-12 mb-3">
                                <label asp-for="Rank.Name" class="form-label"></label>
                                <input asp-for="Rank.Name" class="form-control" placeholder="مثال: عقيد" />
                                <span asp-validation-for="Rank.Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <!-- ترتيب الرتبة -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="Rank.Order" class="form-label"></label>
                                <input asp-for="Rank.Order" class="form-control" type="number" min="1" max="100" />
                                <div class="form-text">الرقم الأصغر يعني رتبة أعلى</div>
                                <span asp-validation-for="Rank.Order" class="text-danger"></span>
                            </div>

                            <!-- الحالة -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحالة</label>
                                <div class="form-check form-switch">
                                    <input asp-for="Rank.IsActive" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="Rank.IsActive" class="form-check-label">
                                        نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- الوصف -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label asp-for="Rank.Description" class="form-label"></label>
                                <textarea asp-for="Rank.Description" class="form-control" rows="3" 
                                          placeholder="وصف اختياري للرتبة..."></textarea>
                                <span asp-validation-for="Rank.Description" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>ملاحظات:</strong>
                                    <ul class="mb-0 mt-2">
                                        <li>ترتيب الرتبة يحدد التسلسل الهرمي (1 = أعلى رتبة)</li>
                                        <li>اسم الرتبة يجب أن يكون فريداً</li>
                                        <li>يمكن إلغاء تفعيل الرتبة بدلاً من حذفها</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-page="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للقائمة
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ الرتبة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
