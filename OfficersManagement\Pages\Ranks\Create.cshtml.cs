using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Ranks
{
    public class CreateModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public CreateModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IActionResult OnGet()
        {
            return Page();
        }

        [BindProperty]
        public Rank Rank { get; set; } = default!;

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            // التحقق من عدم تكرار اسم الرتبة
            var existingRankByName = await _context.Ranks
                .FirstOrDefaultAsync(r => r.Name == Rank.Name);
            
            if (existingRankByName != null)
            {
                ModelState.AddModelError("Rank.Name", "اسم الرتبة موجود مسبقاً");
                return Page();
            }

            // التحقق من عدم تكرار ترتيب الرتبة
            var existingRankByOrder = await _context.Ranks
                .FirstOrDefaultAsync(r => r.Order == Rank.Order);
            
            if (existingRankByOrder != null)
            {
                ModelState.AddModelError("Rank.Order", "ترتيب الرتبة موجود مسبقاً");
                return Page();
            }

            Rank.CreatedAt = DateTime.Now;
            Rank.UpdatedAt = DateTime.Now;

            _context.Ranks.Add(Rank);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "تم إضافة الرتبة بنجاح";
            return RedirectToPage("./Index");
        }
    }
}
