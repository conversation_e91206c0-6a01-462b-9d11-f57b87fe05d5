﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace OfficersManagement.Migrations
{
    /// <inheritdoc />
    public partial class AddRanksTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Rank",
                table: "Officers");

            migrationBuilder.AddColumn<int>(
                name: "RankId",
                table: "Officers",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "Ranks",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Order = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ranks", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 1,
                column: "RankId",
                value: 5);

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 2,
                column: "RankId",
                value: 6);

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 3,
                column: "RankId",
                value: 7);

            migrationBuilder.InsertData(
                table: "Ranks",
                columns: new[] { "Id", "CreatedAt", "Description", "IsActive", "Name", "Order", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "أعلى رتبة عسكرية", true, "فريق أول", 1, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 2, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة عسكرية عليا", true, "فريق", 2, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 3, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة عسكرية عليا", true, "لواء", 3, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 4, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة عسكرية عليا", true, "عميد", 4, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 5, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة ضباط كبار", true, "عقيد", 5, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 6, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة ضباط كبار", true, "مقدم", 6, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 7, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة ضباط", true, "رائد", 7, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 8, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة ضباط", true, "نقيب", 8, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 9, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة ضباط صغار", true, "ملازم أول", 9, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 10, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة ضباط صغار", true, "ملازم", 10, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 11, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة ضباط صف", true, "رقيب أول", 11, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 12, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة ضباط صف", true, "رقيب", 12, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 13, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة أفراد", true, "عريف", 13, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 14, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "رتبة أفراد", true, "جندي أول", 14, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) },
                    { 15, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "أدنى رتبة عسكرية", true, "جندي", 15, new DateTime(2025, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified) }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Officers_RankId",
                table: "Officers",
                column: "RankId");

            migrationBuilder.CreateIndex(
                name: "IX_Ranks_Name",
                table: "Ranks",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Ranks_Order",
                table: "Ranks",
                column: "Order");

            migrationBuilder.AddForeignKey(
                name: "FK_Officers_Ranks_RankId",
                table: "Officers",
                column: "RankId",
                principalTable: "Ranks",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Officers_Ranks_RankId",
                table: "Officers");

            migrationBuilder.DropTable(
                name: "Ranks");

            migrationBuilder.DropIndex(
                name: "IX_Officers_RankId",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "RankId",
                table: "Officers");

            migrationBuilder.AddColumn<string>(
                name: "Rank",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 1,
                column: "Rank",
                value: "عقيد");

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 2,
                column: "Rank",
                value: "مقدم");

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 3,
                column: "Rank",
                value: "رائد");
        }
    }
}
