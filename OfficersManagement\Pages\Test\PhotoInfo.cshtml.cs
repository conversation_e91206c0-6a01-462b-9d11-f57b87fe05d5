using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Test
{
    public class PhotoInfoModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public PhotoInfoModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<Officer> Officers { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Officers = await _context.Officers
                .Include(o => o.Rank)
                .OrderBy(o => o.Id)
                .ToListAsync();
        }
    }
}
