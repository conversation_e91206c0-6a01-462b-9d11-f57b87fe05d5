@page
@model OfficersManagement.Pages.Officers.EditModel
@{
    ViewData["Title"] = "تعديل بيانات الضابط";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        تعديل بيانات الضابط
                    </h4>
                </div>
                <div class="card-body">
                    @if (Model.Officer == null)
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الضابط غير موجود
                        </div>
                    }
                    else
                    {
                        <form method="post" enctype="multipart/form-data">
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                            <input type="hidden" asp-for="Officer.Id" />
                            <input type="hidden" asp-for="Officer.CreatedAt" />

                            <!-- الصورة الشخصية -->
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-image me-2"></i>
                                        الصورة الشخصية
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <!-- معاينة الصورة -->
                                            <div id="photoPreviewMain" class="photo-preview-container mb-3">
                                                @if (Model.Officer.PhotoData != null && Model.Officer.PhotoData.Length > 0)
                                                {
                                                    <img src="data:@Model.Officer.PhotoContentType;base64,@Convert.ToBase64String(Model.Officer.PhotoData)"
                                                         alt="صورة الضابط"
                                                         style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px; cursor: pointer;"
                                                         onclick="zoomImage('data:@Model.Officer.PhotoContentType;base64,@Convert.ToBase64String(Model.Officer.PhotoData)')"
                                                         title="انقر للتكبير" />
                                                }
                                                else
                                                {
                                                    <div style="width: 100%; height: 200px; border: 2px dashed #ddd; display: flex; align-items: center; justify-content: center; border-radius: 8px; background-color: #f8f9fa;">
                                                        <div class="text-center">
                                                            <i class="fas fa-user fa-4x text-muted"></i>
                                                            <p class="text-muted mt-2 mb-0">لا توجد صورة</p>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <!-- أزرار إدارة الصورة -->
                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-outline-primary" onclick="selectFromFile()">
                                                    <i class="fas fa-folder-open me-2"></i>
                                                    اختيار صورة من ملف
                                                </button>
                                                <button type="button" class="btn btn-outline-success" onclick="openCamera()">
                                                    <i class="fas fa-camera me-2"></i>
                                                    التقاط صورة بالكاميرا
                                                </button>
                                                @if (Model.Officer.PhotoData != null && Model.Officer.PhotoData.Length > 0)
                                                {
                                                    <button type="button" class="btn btn-outline-danger" onclick="removeCurrentPhoto()">
                                                        <i class="fas fa-trash me-2"></i>
                                                        إزالة الصورة الحالية
                                                    </button>
                                                }
                                            </div>

                                            <!-- معلومات الصورة -->
                                            @if (Model.Officer.PhotoData != null && Model.Officer.PhotoData.Length > 0)
                                            {
                                                <div class="mt-3">
                                                    <small class="text-muted">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        حجم الصورة: @((Model.Officer.PhotoData.Length / 1024.0).ToString("F1")) كيلوبايت<br>
                                                        نوع الملف: @Model.Officer.PhotoContentType
                                                    </small>
                                                </div>
                                            }

                                            <!-- نصائح -->
                                            <div class="mt-3">
                                                <small class="text-muted">
                                                    <i class="fas fa-lightbulb me-1"></i>
                                                    <strong>نصائح:</strong><br>
                                                    • استخدم صورة واضحة وحديثة<br>
                                                    • الحد الأقصى للحجم: 5 ميجابايت<br>
                                                    • الصيغ المدعومة: JPG, PNG, GIF
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Hidden inputs for photo data -->
                                    <input type="hidden" id="photoData" name="PhotoData" value="" />
                                    <input type="hidden" id="photoType" name="PhotoType" value="" />
                                    <input type="file" id="fileInput" accept="image/*" style="display: none;" />
                                </div>
                            </div>

                            <div class="row">
                                <!-- عرض رقم الموظف (للقراءة فقط) والرقم الإحصائي (قابل للتحرير) -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الموظف</label>
                                    <input type="text" class="form-control" value="@Model.Officer.EmployeeNumber" readonly />
                                    <input type="hidden" asp-for="Officer.EmployeeNumber" />
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label asp-for="Officer.StatisticalNumber" class="form-label"></label>
                                    <input asp-for="Officer.StatisticalNumber" class="form-control" placeholder="أدخل الرقم الإحصائي" />
                                    <span asp-validation-for="Officer.StatisticalNumber" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- الرتبة -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label asp-for="Officer.RankId" class="form-label">الرتبة</label>
                                    <select asp-for="Officer.RankId" asp-items="Model.RanksList" class="form-select">
                                        <option value="">اختر الرتبة</option>
                                    </select>
                                    <span asp-validation-for="Officer.RankId" class="text-danger"></span>
                                    <div class="form-text">
                                        <a asp-page="/Ranks/Create" target="_blank" class="text-decoration-none">
                                            <i class="fas fa-plus me-1"></i>
                                            إضافة رتبة جديدة
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label asp-for="Officer.Title" class="form-label"></label>
                                    <input asp-for="Officer.Title" class="form-control" placeholder="أدخل اللقب" />
                                    <span asp-validation-for="Officer.Title" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- معلومات الوظيفة -->
                            <div class="card mb-4">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-building me-2"></i>
                                        معلومات الوظيفة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <!-- الوكالة والمديرية -->
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="Officer.AgencyId" class="form-label">الوكالة</label>
                                            <select asp-for="Officer.AgencyId" asp-items="Model.AgenciesList" class="form-select" id="agencySelect" onchange="loadDirectorates()">
                                                <option value="">اختر الوكالة</option>
                                            </select>
                                            <span asp-validation-for="Officer.AgencyId" class="text-danger"></span>
                                            <div class="form-text">
                                                <a asp-page="/Agencies/Create" target="_blank" class="text-decoration-none">
                                                    <i class="fas fa-plus me-1"></i>
                                                    إضافة وكالة جديدة
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="Officer.DirectorateId" class="form-label">المديرية</label>
                                            <select asp-for="Officer.DirectorateId" class="form-select" id="directorateSelect" onchange="loadDepartments()" disabled>
                                                <option value="">اختر المديرية أولاً</option>
                                            </select>
                                            <span asp-validation-for="Officer.DirectorateId" class="text-danger"></span>
                                            <div class="form-text">
                                                <a asp-page="/Directorates/Create" target="_blank" class="text-decoration-none">
                                                    <i class="fas fa-plus me-1"></i>
                                                    إضافة مديرية جديدة
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- القسم والشعبة -->
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="Officer.DepartmentId" class="form-label">القسم</label>
                                            <select asp-for="Officer.DepartmentId" class="form-select" id="departmentSelect" onchange="loadDivisions()" disabled>
                                                <option value="">اختر القسم أولاً</option>
                                            </select>
                                            <span asp-validation-for="Officer.DepartmentId" class="text-danger"></span>
                                            <div class="form-text">
                                                <a asp-page="/Departments/Create" target="_blank" class="text-decoration-none">
                                                    <i class="fas fa-plus me-1"></i>
                                                    إضافة قسم جديد
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="Officer.DivisionId" class="form-label">الشعبة</label>
                                            <select asp-for="Officer.DivisionId" class="form-select" id="divisionSelect" disabled>
                                                <option value="">اختر الشعبة أولاً</option>
                                            </select>
                                            <span asp-validation-for="Officer.DivisionId" class="text-danger"></span>
                                            <div class="form-text">
                                                <a asp-page="/Divisions/Create" target="_blank" class="text-decoration-none">
                                                    <i class="fas fa-plus me-1"></i>
                                                    إضافة شعبة جديدة
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الاسم الرباعي -->
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label asp-for="Officer.FirstName" class="form-label"></label>
                                    <input asp-for="Officer.FirstName" class="form-control" placeholder="أدخل الاسم الأول" />
                                    <span asp-validation-for="Officer.FirstName" class="text-danger"></span>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label asp-for="Officer.SecondName" class="form-label"></label>
                                    <input asp-for="Officer.SecondName" class="form-control" placeholder="أدخل الاسم الثاني" />
                                    <span asp-validation-for="Officer.SecondName" class="text-danger"></span>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label asp-for="Officer.ThirdName" class="form-label"></label>
                                    <input asp-for="Officer.ThirdName" class="form-control" placeholder="أدخل الاسم الثالث" />
                                    <span asp-validation-for="Officer.ThirdName" class="text-danger"></span>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label asp-for="Officer.FourthName" class="form-label"></label>
                                    <input asp-for="Officer.FourthName" class="form-control" placeholder="أدخل الاسم الرابع" />
                                    <span asp-validation-for="Officer.FourthName" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- محل الولادة وتاريخ الولادة -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label asp-for="Officer.PlaceOfBirth" class="form-label"></label>
                                    <input asp-for="Officer.PlaceOfBirth" class="form-control" />
                                    <span asp-validation-for="Officer.PlaceOfBirth" class="text-danger"></span>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label asp-for="Officer.DateOfBirth" class="form-label"></label>
                                    <input asp-for="Officer.DateOfBirth" class="form-control" type="date" />
                                    <span asp-validation-for="Officer.DateOfBirth" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- الحالة الاجتماعية وفصيلة الدم والحالة الصحية -->
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Officer.MaritalStatus" class="form-label"></label>
                                    <select asp-for="Officer.MaritalStatus" class="form-select">
                                        <option value="">اختر الحالة الاجتماعية</option>
                                        <option value="أعزب">أعزب</option>
                                        <option value="متزوج">متزوج</option>
                                        <option value="مطلق">مطلق</option>
                                        <option value="أرمل">أرمل</option>
                                    </select>
                                    <span asp-validation-for="Officer.MaritalStatus" class="text-danger"></span>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Officer.BloodType" class="form-label"></label>
                                    <select asp-for="Officer.BloodType" class="form-select">
                                        <option value="">اختر فصيلة الدم</option>
                                        <option value="A+">A+</option>
                                        <option value="A-">A-</option>
                                        <option value="B+">B+</option>
                                        <option value="B-">B-</option>
                                        <option value="AB+">AB+</option>
                                        <option value="AB-">AB-</option>
                                        <option value="O+">O+</option>
                                        <option value="O-">O-</option>
                                    </select>
                                    <span asp-validation-for="Officer.BloodType" class="text-danger"></span>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Officer.HealthStatus" class="form-label"></label>
                                    <select asp-for="Officer.HealthStatus" class="form-select">
                                        <option value="">اختر الحالة الصحية</option>
                                        <option value="سليم">سليم</option>
                                        <option value="مريض">مريض</option>
                                        <option value="معاق">معاق</option>
                                        <option value="مصاب في الخدمة">مصاب في الخدمة</option>
                                        <option value="تحت العلاج">تحت العلاج</option>
                                    </select>
                                    <span asp-validation-for="Officer.HealthStatus" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- معلومات السكن -->
                            <div class="card mb-4">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-map-marker-alt me-2"></i>
                                        معلومات السكن
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <!-- المحافظة والقضاء والناحية -->
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label asp-for="Officer.ProvinceId" class="form-label">المحافظة</label>
                                            <select asp-for="Officer.ProvinceId" asp-items="Model.ProvincesList" class="form-select">
                                                <option value="">اختر المحافظة</option>
                                            </select>
                                            <span asp-validation-for="Officer.ProvinceId" class="text-danger"></span>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label asp-for="Officer.District" class="form-label">القضاء</label>
                                            <input asp-for="Officer.District" class="form-control" placeholder="أدخل اسم القضاء" />
                                            <span asp-validation-for="Officer.District" class="text-danger"></span>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label asp-for="Officer.Subdistrict" class="form-label">الناحية</label>
                                            <input asp-for="Officer.Subdistrict" class="form-control" placeholder="أدخل اسم الناحية" />
                                            <span asp-validation-for="Officer.Subdistrict" class="text-danger"></span>
                                        </div>
                                    </div>

                                    <!-- القرية والحي والمحلة -->
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label asp-for="Officer.Village" class="form-label">القرية</label>
                                            <input asp-for="Officer.Village" class="form-control" placeholder="أدخل اسم القرية" />
                                            <span asp-validation-for="Officer.Village" class="text-danger"></span>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label asp-for="Officer.Neighborhood" class="form-label">الحي</label>
                                            <input asp-for="Officer.Neighborhood" class="form-control" placeholder="أدخل اسم الحي" />
                                            <span asp-validation-for="Officer.Neighborhood" class="text-danger"></span>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label asp-for="Officer.Locality" class="form-label">المحلة</label>
                                            <input asp-for="Officer.Locality" class="form-control" placeholder="أدخل اسم المحلة" />
                                            <span asp-validation-for="Officer.Locality" class="text-danger"></span>
                                        </div>
                                    </div>

                                    <!-- الزقاق ورقم الدار وأقرب نقطة دالة -->
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label asp-for="Officer.Alley" class="form-label">الزقاق</label>
                                            <input asp-for="Officer.Alley" class="form-control" placeholder="أدخل اسم الزقاق" />
                                            <span asp-validation-for="Officer.Alley" class="text-danger"></span>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label asp-for="Officer.HouseNumber" class="form-label">رقم الدار</label>
                                            <input asp-for="Officer.HouseNumber" class="form-control" placeholder="أدخل رقم الدار" />
                                            <span asp-validation-for="Officer.HouseNumber" class="text-danger"></span>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label asp-for="Officer.NearestLandmark" class="form-label">أقرب نقطة دالة</label>
                                            <input asp-for="Officer.NearestLandmark" class="form-control" placeholder="أدخل أقرب نقطة دالة" />
                                            <span asp-validation-for="Officer.NearestLandmark" class="text-danger"></span>
                                        </div>
                                    </div>

                                    <!-- معاينة العنوان بالتفصيل -->
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="alert alert-info">
                                                <h6 class="alert-heading">
                                                    <i class="fas fa-eye me-2"></i>
                                                    معاينة العنوان بالتفصيل
                                                </h6>
                                                <div class="bg-white p-2 rounded border">
                                                    <strong>العنوان بالتفصيل:</strong>
                                                    <div id="addressPreview" class="mt-1 text-primary fw-bold">@Model.Officer.DetailedAddress</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات إضافية -->
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">معلومات إضافية</h6>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <strong>الاسم الكامل:</strong><br>
                                                    <span>@Model.Officer.FullName</span>
                                                </div>
                                                <div class="col-md-4">
                                                    <strong>العمر الحالي:</strong><br>
                                                    <span class="text-info">@Model.Officer.Age</span>
                                                </div>
                                                <div class="col-md-4">
                                                    <strong>تاريخ الإنشاء:</strong><br>
                                                    <span class="text-muted">@Model.Officer.CreatedAt.ToString("yyyy/MM/dd")</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الإجراءات -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <a asp-page="./Index" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-1"></i>
                                            العودة للقائمة
                                        </a>
                                        <div>
                                            <a asp-page="./Details" asp-route-id="@Model.Officer.Id" class="btn btn-info me-2">
                                                <i class="fas fa-eye me-1"></i>
                                                عرض التفاصيل
                                            </a>
                                            <button type="submit" class="btn btn-warning">
                                                <i class="fas fa-save me-1"></i>
                                                حفظ التغييرات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Camera Modal -->
<div class="modal fade" id="cameraModal" tabindex="-1" aria-labelledby="cameraModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cameraModalLabel">
                    <i class="fas fa-camera me-2"></i>
                    التقاط صورة بالكاميرا
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <!-- Camera Selection -->
                <div class="mb-3">
                    <label for="cameraSelect" class="form-label">اختيار الكاميرا:</label>
                    <select id="cameraSelect" class="form-select" onchange="switchCamera()">
                        <option value="">جاري البحث عن الكاميرات...</option>
                    </select>
                </div>

                <!-- Camera Status -->
                <div id="cameraStatus" class="alert alert-info" style="display: none;">
                    جاري تحميل الكاميرا...
                </div>

                <!-- Video Element -->
                <video id="cameraVideo" autoplay playsinline style="width: 100%; max-width: 640px; height: auto; border-radius: 8px; display: none;"></video>

                <!-- Canvas for captured image -->
                <canvas id="cameraCanvas" style="width: 100%; max-width: 640px; height: auto; border-radius: 8px; display: none;"></canvas>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" id="captureBtn" class="btn btn-primary" onclick="capturePhoto()" disabled>
                    <i class="fas fa-camera me-1"></i>
                    التقاط الصورة
                </button>
                <button type="button" id="retakeBtn" class="btn btn-warning" onclick="retakePhoto()" style="display: none;">
                    <i class="fas fa-redo me-1"></i>
                    إعادة التقاط
                </button>
                <button type="button" id="usePhotoBtn" class="btn btn-success" onclick="usePhoto()" disabled>
                    <i class="fas fa-check me-1"></i>
                    استخدام هذه الصورة
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Image Zoom Modal -->
<div class="modal fade" id="imageZoomModal" tabindex="-1" aria-labelledby="imageZoomModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageZoomModalLabel">
                    <i class="fas fa-search-plus me-2"></i>
                    عرض الصورة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0" style="overflow: hidden;">
                <div class="position-relative" style="height: 70vh; overflow: hidden;">
                    <img id="zoomedImage" src="" alt="صورة مكبرة"
                         style="max-width: 100%; max-height: 100%; object-fit: contain; transition: transform 0.2s; cursor: grab;" />
                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-outline-primary" onclick="zoomIn()">
                    <i class="fas fa-search-plus me-1"></i>
                    تكبير
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="zoomOut()">
                    <i class="fas fa-search-minus me-1"></i>
                    تصغير
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="resetZoom()">
                    <i class="fas fa-expand-arrows-alt me-1"></i>
                    إعادة تعيين
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>



@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <style>
        .photo-preview-container {
            border: 2px dashed #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: border-color 0.3s ease;
        }

        .photo-preview-container:hover {
            border-color: #007bff;
        }

        .photo-preview-container img {
            transition: transform 0.3s ease;
        }

        .photo-preview-container img:hover {
            transform: scale(1.05);
        }

        .btn-outline-primary:hover,
        .btn-outline-success:hover,
        .btn-outline-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .card-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
        }

        .bg-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
        }

        #cameraVideo, #cameraCanvas {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .modal-content {
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn {
            transition: all 0.3s ease;
        }

        .alert {
            border-radius: 8px;
        }
    </style>



    <script>
        // تحديد الحد الأقصى لتاريخ الولادة (اليوم)
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة بنجاح');



            const dateInput = document.querySelector('input[type="date"]');
            if (dateInput) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.setAttribute('max', today);
            }



            // إعداد تحديث معاينة العنوان
            setupAddressPreview();

            function setupAddressPreview() {
                const addressFields = [
                    'Officer.ProvinceId',
                    'Officer.District',
                    'Officer.Subdistrict',
                    'Officer.Village',
                    'Officer.Neighborhood',
                    'Officer.Locality',
                    'Officer.Alley',
                    'Officer.HouseNumber',
                    'Officer.NearestLandmark'
                ];

                addressFields.forEach(fieldName => {
                    const field = document.querySelector(`[name="${fieldName}"]`);
                    if (field) {
                        field.addEventListener('input', updateAddressPreview);
                        field.addEventListener('change', updateAddressPreview);
                    }
                });

                // تحديث أولي
                updateAddressPreview();
            }

            function updateAddressPreview() {
                const addressParts = [];

                // المحافظة
                const provinceSelect = document.querySelector('[name="Officer.ProvinceId"]');
                if (provinceSelect && provinceSelect.value) {
                    const selectedOption = provinceSelect.options[provinceSelect.selectedIndex];
                    if (selectedOption && selectedOption.text !== 'اختر المحافظة') {
                        addressParts.push(`محافظة ${selectedOption.text}`);
                    }
                }

                // القضاء
                const district = document.querySelector('[name="Officer.District"]')?.value?.trim();
                if (district) {
                    addressParts.push(`قضاء ${district}`);
                }

                // الناحية
                const subdistrict = document.querySelector('[name="Officer.Subdistrict"]')?.value?.trim();
                if (subdistrict) {
                    addressParts.push(`ناحية ${subdistrict}`);
                }

                // القرية
                const village = document.querySelector('[name="Officer.Village"]')?.value?.trim();
                if (village) {
                    addressParts.push(`قرية ${village}`);
                }

                // الحي
                const neighborhood = document.querySelector('[name="Officer.Neighborhood"]')?.value?.trim();
                if (neighborhood) {
                    addressParts.push(`حي ${neighborhood}`);
                }

                // المحلة
                const locality = document.querySelector('[name="Officer.Locality"]')?.value?.trim();
                if (locality) {
                    addressParts.push(`محلة ${locality}`);
                }

                // الزقاق
                const alley = document.querySelector('[name="Officer.Alley"]')?.value?.trim();
                if (alley) {
                    addressParts.push(`زقاق ${alley}`);
                }

                // رقم الدار
                const houseNumber = document.querySelector('[name="Officer.HouseNumber"]')?.value?.trim();
                if (houseNumber) {
                    addressParts.push(`دار رقم ${houseNumber}`);
                }

                // أقرب نقطة دالة
                const landmark = document.querySelector('[name="Officer.NearestLandmark"]')?.value?.trim();
                if (landmark) {
                    addressParts.push(`قرب ${landmark}`);
                }

                const addressPreview = document.getElementById('addressPreview');
                if (addressPreview) {
                    if (addressParts.length > 0) {
                        addressPreview.textContent = addressParts.join(' - ');
                        addressPreview.className = 'text-primary fw-bold';
                    } else {
                        addressPreview.textContent = 'غير محدد';
                        addressPreview.className = 'text-muted';
                    }
                }
            }

        });



        function selectFromFile() {
            console.log('تم النقر على اختيار من ملف');
            try {
                const fileInput = document.getElementById('fileInput');
                if (fileInput) {
                    console.log('تم العثور على عنصر fileInput، سيتم فتح نافذة اختيار الملف');
                    fileInput.value = ''; // مسح القيمة السابقة لضمان تشغيل الحدث حتى لو تم اختيار نفس الملف
                    fileInput.click();
                } else {
                    console.error('لم يتم العثور على عنصر fileInput');
                    alert('خطأ: لم يتم العثور على عنصر اختيار الملف');
                }
            } catch (err) {
                console.error('خطأ في selectFromFile:', err);
                alert('حدث خطأ في فتح نافذة اختيار الملف: ' + err.message);
            }
        }

        function openCamera() {
            console.log('تم النقر على فتح الكاميرا');
            try {
                const cameraModal = document.getElementById('cameraModal');
                if (cameraModal) {
                    const modal = new bootstrap.Modal(cameraModal);
                    modal.show();

                    // إعادة تعيين حالة الكاميرا
                    resetCameraState();

                    // البحث عن الكاميرات المتاحة
                    getCameras().then(() => {
                        startCamera();
                    });
                } else {
                    console.error('لم يتم العثور على مودال الكاميرا');
                }
            } catch (err) {
                console.error('خطأ في openCamera:', err);
                alert('حدث خطأ في فتح الكاميرا: ' + err.message);
            }
        }

        async function getCameras() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                availableCameras = devices.filter(device => device.kind === 'videoinput');

                const cameraSelect = document.getElementById('cameraSelect');
                cameraSelect.innerHTML = '';

                if (availableCameras.length === 0) {
                    cameraSelect.innerHTML = '<option value="">لا توجد كاميرات متاحة</option>';
                    return;
                }

                availableCameras.forEach((camera, index) => {
                    const option = document.createElement('option');
                    option.value = camera.deviceId;
                    option.textContent = camera.label || `كاميرا ${index + 1}`;
                    cameraSelect.appendChild(option);
                });

                // اختيار أول كاميرا افتراضياً
                if (availableCameras.length > 0) {
                    selectedCameraId = availableCameras[0].deviceId;
                    cameraSelect.value = selectedCameraId;
                }

                console.log(`تم العثور على ${availableCameras.length} كاميرا`);

            } catch (err) {
                console.error('خطأ في البحث عن الكاميرات:', err);
                const cameraSelect = document.getElementById('cameraSelect');
                cameraSelect.innerHTML = '<option value="">خطأ في البحث عن الكاميرات</option>';
            }
        }

        function switchCamera() {
            const cameraSelect = document.getElementById('cameraSelect');
            selectedCameraId = cameraSelect.value;

            if (selectedCameraId) {
                console.log('تبديل إلى الكاميرا:', selectedCameraId);
                stopCamera();
                resetCameraState();
                startCamera();
            }
        }

        function resetCameraState() {
            const video = document.getElementById('cameraVideo');
            const canvas = document.getElementById('cameraCanvas');
            const captureBtn = document.getElementById('captureBtn');
            const retakeBtn = document.getElementById('retakeBtn');
            const usePhotoBtn = document.getElementById('usePhotoBtn');
            const status = document.getElementById('cameraStatus');

            video.style.display = 'block';
            canvas.style.display = 'none';
            captureBtn.disabled = true;
            retakeBtn.style.display = 'none';
            usePhotoBtn.disabled = true;
            status.style.display = 'block';
            status.textContent = 'جاري تحميل الكاميرا...';
            capturedImageData = null;
        }

        async function startCamera() {
            try {
                const video = document.getElementById('cameraVideo');
                const status = document.getElementById('cameraStatus');
                const captureBtn = document.getElementById('captureBtn');

                // إعداد قيود الكاميرا
                const constraints = {
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    },
                    audio: false
                };

                // إضافة معرف الكاميرا المحددة إذا كان متاحاً
                if (selectedCameraId) {
                    constraints.video.deviceId = { exact: selectedCameraId };
                } else {
                    constraints.video.facingMode = 'user'; // الكاميرا الأمامية افتراضياً
                }

                // طلب إذن الكاميرا
                currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                video.srcObject = currentStream;

                // انتظار تحميل الفيديو قبل تشغيله
                video.onloadedmetadata = function() {
                    video.play().then(() => {
                        status.style.display = 'none';
                        captureBtn.disabled = false;
                        console.log('الكاميرا جاهزة');
                    }).catch(err => {
                        console.error('خطأ في تشغيل الفيديو:', err);
                        status.textContent = 'خطأ في تشغيل الكاميرا';
                    });
                };

                video.onerror = function(err) {
                    console.error('خطأ في الفيديو:', err);
                    status.textContent = 'خطأ في الكاميرا';
                };

            } catch (err) {
                console.error('خطأ في الكاميرا:', err);
                const status = document.getElementById('cameraStatus');
                status.textContent = 'فشل في الوصول للكاميرا';

                let errorMessage = 'لا يمكن الوصول إلى الكاميرا.';
                if (err.name === 'NotAllowedError') {
                    errorMessage = 'تم رفض إذن الكاميرا. يرجى السماح للمتصفح بالوصول للكاميرا.';
                } else if (err.name === 'NotFoundError') {
                    errorMessage = 'لم يتم العثور على كاميرا متاحة.';
                } else if (err.name === 'NotReadableError') {
                    errorMessage = 'الكاميرا مستخدمة من تطبيق آخر.';
                } else if (err.name === 'OverconstrainedError') {
                    errorMessage = 'الكاميرا المحددة غير متاحة. جاري المحاولة مع كاميرا أخرى...';
                    // محاولة مع كاميرا افتراضية
                    selectedCameraId = null;
                    setTimeout(() => startCamera(), 1000);
                    return;
                }

                alert(errorMessage);
            }
        }

        function capturePhoto() {
            const video = document.getElementById('cameraVideo');
            const canvas = document.getElementById('cameraCanvas');
            const ctx = canvas.getContext('2d');
            const captureBtn = document.getElementById('captureBtn');
            const retakeBtn = document.getElementById('retakeBtn');
            const usePhotoBtn = document.getElementById('usePhotoBtn');

            // التأكد من أن الفيديو جاهز
            if (video.videoWidth === 0 || video.videoHeight === 0) {
                alert('الكاميرا غير جاهزة بعد، يرجى المحاولة مرة أخرى');
                return;
            }

            try {
                // تعيين حجم الكانفاس بناءً على حجم الفيديو الفعلي
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;

                // رسم الصورة من الفيديو إلى الكانفاس
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

                // إخفاء الفيديو وإظهار الكانفاس
                video.style.display = 'none';
                canvas.style.display = 'block';

                // تحديث حالة الأزرار
                captureBtn.style.display = 'none';
                retakeBtn.style.display = 'inline-block';
                usePhotoBtn.disabled = false;

                // حفظ بيانات الصورة بجودة مضغوطة
                capturedImageData = canvas.toDataURL('image/jpeg', 0.7);
                console.log('تم التقاط الصورة بنجاح');

            } catch (err) {
                console.error('خطأ في التقاط الصورة:', err);
                alert('حدث خطأ أثناء التقاط الصورة');
            }
        }

        function retakePhoto() {
            const video = document.getElementById('cameraVideo');
            const canvas = document.getElementById('cameraCanvas');
            const captureBtn = document.getElementById('captureBtn');
            const retakeBtn = document.getElementById('retakeBtn');
            const usePhotoBtn = document.getElementById('usePhotoBtn');

            // إظهار الفيديو وإخفاء الكانفاس
            video.style.display = 'block';
            canvas.style.display = 'none';

            // تحديث حالة الأزرار
            captureBtn.style.display = 'inline-block';
            captureBtn.disabled = false;
            retakeBtn.style.display = 'none';
            usePhotoBtn.disabled = true;

            // مسح البيانات المحفوظة
            capturedImageData = null;
            console.log('تم إعادة تعيين الكاميرا');
        }

        function usePhoto() {
            if (capturedImageData) {
                try {
                    // عرض الصورة في المعاينة
                    const photoPreview = document.getElementById('photoPreviewMain');
                    photoPreview.innerHTML = `<img src="${capturedImageData}" alt="صورة ملتقطة" style="width: 100%; height: 100%; object-fit: fill; display: block; border-radius: 6px; cursor: pointer;" onclick="zoomImage('${capturedImageData}')" title="انقر للتكبير" />`;

                    // حفظ بيانات الصورة للإرسال
                    document.getElementById('photoData').value = capturedImageData;
                    document.getElementById('photoType').value = 'camera';

                    console.log('تم حفظ الصورة للإرسال');

                    // إغلاق الكاميرا والمودال
                    stopCamera();
                    const modal = bootstrap.Modal.getInstance(document.getElementById('cameraModal'));
                    modal.hide();

                } catch (err) {
                    console.error('خطأ في استخدام الصورة:', err);
                    alert('حدث خطأ أثناء حفظ الصورة');
                }
            } else {
                alert('لم يتم التقاط أي صورة');
            }
        }

        function stopCamera() {
            try {
                if (currentStream) {
                    currentStream.getTracks().forEach(track => {
                        track.stop();
                        console.log('تم إيقاف track:', track.kind);
                    });
                    currentStream = null;
                    console.log('تم إيقاف الكاميرا');
                }
            } catch (err) {
                console.error('خطأ في إيقاف الكاميرا:', err);
            }
        }

        // إغلاق الكاميرا عند إغلاق المودال
        document.getElementById('cameraModal').addEventListener('hidden.bs.modal', function () {
            console.log('تم إغلاق مودال الكاميرا');
            stopCamera();
            resetCameraState();
        });

        // إغلاق الكاميرا عند إغلاق الصفحة
        window.addEventListener('beforeunload', function() {
            stopCamera();
        });

        // وظيفة إزالة الصورة الحالية
        function removeCurrentPhoto() {
            console.log('تم النقر على إزالة الصورة');
            try {
                if (confirm('هل أنت متأكد من إزالة الصورة الحالية؟')) {
                    const photoPreview = document.getElementById('photoPreviewMain');
                    const photoData = document.getElementById('photoData');
                    const photoType = document.getElementById('photoType');

                    if (photoPreview && photoData && photoType) {
                        photoPreview.innerHTML = `
                            <div style="display: flex; align-items: center; justify-content: center; height: 100%;">
                                <div class="text-center">
                                    <i class="fas fa-user fa-4x text-muted"></i>
                                    <p class="text-muted mt-2 mb-0">لا توجد صورة</p>
                                </div>
                            </div>
                        `;

                        // تعيين قيم لإزالة الصورة
                        photoData.value = 'REMOVE_PHOTO';
                        photoType.value = 'remove';

                        console.log('تم تحديد الصورة للإزالة');

                        // إعادة تحميل الصفحة لتحديث الأزرار
                        setTimeout(() => {
                            location.reload();
                        }, 500);
                    } else {
                        console.error('لم يتم العثور على العناصر المطلوبة');
                    }
                }
            } catch (err) {
                console.error('خطأ في removeCurrentPhoto:', err);
                alert('حدث خطأ في إزالة الصورة: ' + err.message);
            }
        }

        // إغلاق الكاميرا عند إغلاق المودال
        document.getElementById('cameraModal').addEventListener('hidden.bs.modal', function () {
            console.log('تم إغلاق مودال الكاميرا');
            stopCamera();
            resetCameraState();
        });

        // إغلاق الكاميرا عند إغلاق الصفحة
        window.addEventListener('beforeunload', function() {
            stopCamera();
        });

        document.getElementById('fileInput').addEventListener('change', function(e) {
            handleImageSelection(e.target.files[0]);
        });

        function handleImageSelection(file) {
            if (file) {
                console.log('تم اختيار ملف:', file.name, 'حجم:', file.size);

                // التحقق من نوع الملف
                if (!file.type.startsWith('image/')) {
                    alert('يرجى اختيار ملف صورة صحيح');
                    return;
                }

                // التحقق من حجم الملف (أقل من 5 ميجابايت)
                if (file.size > 5 * 1024 * 1024) {
                    alert('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
                    return;
                }

                // ضغط الصورة قبل المعالجة
                compressImage(file, function(compressedDataUrl) {
                    console.log('تم ضغط الصورة بنجاح');
                    const photoPreview = document.getElementById('photoPreviewMain');
                    if (photoPreview) {
                        photoPreview.innerHTML = `<img src="${compressedDataUrl}" alt="معاينة الصورة" style="width: 100%; height: 100%; object-fit: fill; display: block; border-radius: 6px; cursor: pointer;" onclick="zoomImage('${compressedDataUrl}')" title="انقر للتكبير" />`;
                        console.log('تم تحديث معاينة الصورة');
                    }

                    // حفظ بيانات الصورة المضغوطة للإرسال
                    const photoDataInput = document.getElementById('photoData');
                    const photoTypeInput = document.getElementById('photoType');

                    if (photoDataInput && photoTypeInput) {
                        photoDataInput.value = compressedDataUrl;
                        photoTypeInput.value = 'file';
                        console.log('تم حفظ بيانات الصورة المضغوطة');
                    }
                }, function(error) {
                    console.error('خطأ في ضغط الصورة:', error);
                    alert('حدث خطأ في ضغط الصورة');
                });
            }
        }

        // دالة ضغط الصور
        function compressImage(file, successCallback, errorCallback) {
            const maxWidth = 800;
            const maxHeight = 600;
            const quality = 0.7;

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = function() {
                // حساب الأبعاد الجديدة مع الحفاظ على النسبة
                let { width, height } = calculateNewDimensions(img.width, img.height, maxWidth, maxHeight);

                canvas.width = width;
                canvas.height = height;

                // رسم الصورة المضغوطة
                ctx.drawImage(img, 0, 0, width, height);

                // تحويل إلى base64 مع ضغط
                const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);

                console.log(`تم ضغط الصورة من ${img.width}x${img.height} إلى ${width}x${height}`);
                console.log(`حجم الملف الأصلي: ${(file.size / 1024).toFixed(1)} KB`);

                // تقدير حجم الصورة المضغوطة
                const compressedSize = Math.round((compressedDataUrl.length - 'data:image/jpeg;base64,'.length) * 3/4);
                console.log(`حجم الصورة المضغوطة: ${(compressedSize / 1024).toFixed(1)} KB`);

                successCallback(compressedDataUrl);
            };

            img.onerror = function() {
                errorCallback('فشل في تحميل الصورة');
            };

            const reader = new FileReader();
            reader.onload = function(e) {
                img.src = e.target.result;
            };
            reader.onerror = function() {
                errorCallback('فشل في قراءة الملف');
            };
            reader.readAsDataURL(file);
        }

        function calculateNewDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
            if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
                return { width: originalWidth, height: originalHeight };
            }

            const ratioX = maxWidth / originalWidth;
            const ratioY = maxHeight / originalHeight;
            const ratio = Math.min(ratioX, ratioY);

            return {
                width: Math.round(originalWidth * ratio),
                height: Math.round(originalHeight * ratio)
            };
        }

        // وظائف التكبير
        let currentZoom = 1;
        let isDragging = false;
        let startX, startY, translateX = 0, translateY = 0;

        function zoomImage(imageSrc) {
            console.log('فتح صورة للتكبير:', imageSrc);
            const modal = new bootstrap.Modal(document.getElementById('imageZoomModal'));
            const zoomedImage = document.getElementById('zoomedImage');

            zoomedImage.src = imageSrc;
            resetZoom();
            modal.show();
        }

        function zoomIn() {
            currentZoom = Math.min(currentZoom * 1.2, 5); // حد أقصى 5x
            updateImageTransform();
        }

        function zoomOut() {
            currentZoom = Math.max(currentZoom / 1.2, 0.5); // حد أدنى 0.5x
            updateImageTransform();
        }

        function resetZoom() {
            currentZoom = 1;
            translateX = 0;
            translateY = 0;
            updateImageTransform();
        }

        function updateImageTransform() {
            const zoomedImage = document.getElementById('zoomedImage');
            zoomedImage.style.transform = `scale(${currentZoom}) translate(${translateX}px, ${translateY}px)`;
        }

        // إضافة وظائف السحب للصورة المكبرة
        document.addEventListener('DOMContentLoaded', function() {
            const zoomedImage = document.getElementById('zoomedImage');

            if (zoomedImage) {
                zoomedImage.addEventListener('mousedown', function(e) {
                    if (currentZoom > 1) {
                        isDragging = true;
                        startX = e.clientX - translateX;
                        startY = e.clientY - translateY;
                        zoomedImage.style.cursor = 'grabbing';
                        e.preventDefault();
                    }
                });

                document.addEventListener('mousemove', function(e) {
                    if (isDragging && currentZoom > 1) {
                        translateX = e.clientX - startX;
                        translateY = e.clientY - startY;
                        updateImageTransform();
                    }
                });

                document.addEventListener('mouseup', function() {
                    if (isDragging) {
                        isDragging = false;
                        const zoomedImage = document.getElementById('zoomedImage');
                        if (zoomedImage) {
                            zoomedImage.style.cursor = currentZoom > 1 ? 'grab' : 'default';
                        }
                    }
                });

                // تكبير بالعجلة
                zoomedImage.addEventListener('wheel', function(e) {
                    e.preventDefault();
                    if (e.deltaY < 0) {
                        zoomIn();
                    } else {
                        zoomOut();
                    }
                });
            }
        });

        // دوال الكومبوبوكس المترابط
        async function loadDirectorates() {
            const agencyId = document.getElementById('agencySelect').value;
            const directorateSelect = document.getElementById('directorateSelect');
            const departmentSelect = document.getElementById('departmentSelect');
            const divisionSelect = document.getElementById('divisionSelect');

            // إعادة تعيين القوائم التابعة
            directorateSelect.innerHTML = '<option value="">جاري التحميل...</option>';
            departmentSelect.innerHTML = '<option value="">اختر القسم أولاً</option>';
            divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';

            departmentSelect.disabled = true;
            divisionSelect.disabled = true;

            if (!agencyId) {
                directorateSelect.innerHTML = '<option value="">اختر المديرية أولاً</option>';
                directorateSelect.disabled = true;
                return;
            }

            try {
                const response = await fetch(`/Officers/Create?handler=Directorates&agencyId=${agencyId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const directorates = await response.json();

                directorateSelect.innerHTML = '<option value="">اختر المديرية</option>';
                directorates.forEach(directorate => {
                    directorateSelect.innerHTML += `<option value="${directorate.id}">${directorate.name}</option>`;
                });

                directorateSelect.disabled = false;
            } catch (error) {
                console.error('خطأ في تحميل المديريات:', error);
                directorateSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
            }
        }

        async function loadDepartments() {
            const directorateId = document.getElementById('directorateSelect').value;
            const departmentSelect = document.getElementById('departmentSelect');
            const divisionSelect = document.getElementById('divisionSelect');

            // إعادة تعيين القوائم التابعة
            departmentSelect.innerHTML = '<option value="">جاري التحميل...</option>';
            divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';
            divisionSelect.disabled = true;

            if (!directorateId) {
                departmentSelect.innerHTML = '<option value="">اختر القسم أولاً</option>';
                departmentSelect.disabled = true;
                return;
            }

            try {
                const response = await fetch(`/Officers/Create?handler=Departments&directorateId=${directorateId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const departments = await response.json();

                departmentSelect.innerHTML = '<option value="">اختر القسم</option>';
                departments.forEach(department => {
                    departmentSelect.innerHTML += `<option value="${department.id}">${department.name}</option>`;
                });

                departmentSelect.disabled = false;
            } catch (error) {
                console.error('خطأ في تحميل الأقسام:', error);
                departmentSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
            }
        }

        async function loadDivisions() {
            const departmentId = document.getElementById('departmentSelect').value;
            const divisionSelect = document.getElementById('divisionSelect');

            // إعادة تعيين القوائم التابعة
            divisionSelect.innerHTML = '<option value="">جاري التحميل...</option>';

            if (!departmentId) {
                divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';
                divisionSelect.disabled = true;
                return;
            }

            try {
                const response = await fetch(`/Officers/Create?handler=Divisions&departmentId=${departmentId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const divisions = await response.json();

                divisionSelect.innerHTML = '<option value="">اختر الشعبة</option>';
                divisions.forEach(division => {
                    divisionSelect.innerHTML += `<option value="${division.id}">${division.name}</option>`;
                });

                divisionSelect.disabled = false;
            } catch (error) {
                console.error('خطأ في تحميل الشعب:', error);
                divisionSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
            }
        }

        // مستمع الرسائل من النوافذ الفرعية
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type) {
                switch(event.data.type) {
                    case 'RANK_CREATED':
                        refreshRanks();
                        showNotification('تم إضافة الرتبة بنجاح وتحديث القائمة', 'success');
                        break;
                    case 'AGENCY_CREATED':
                        refreshAgencies();
                        showNotification('تم إضافة الوكالة بنجاح وتحديث القائمة', 'success');
                        break;
                    case 'DIRECTORATE_CREATED':
                        refreshDirectorates();
                        showNotification('تم إضافة المديرية بنجاح وتحديث القائمة', 'success');
                        break;
                    case 'DEPARTMENT_CREATED':
                        refreshDepartments();
                        showNotification('تم إضافة القسم بنجاح وتحديث القائمة', 'success');
                        break;
                    case 'DIVISION_CREATED':
                        refreshDivisions();
                        showNotification('تم إضافة الشعبة بنجاح وتحديث القائمة', 'success');
                        break;
                }
            }
        });

        // دوال تحديث الكومبوبوكس
        async function refreshRanks() {
            try {
                const response = await fetch('/Officers/Create?handler=Ranks');
                if (!response.ok) throw new Error('Failed to fetch ranks');
                const ranks = await response.json();

                const rankSelect = document.getElementById('rankSelect');
                const currentValue = rankSelect.value;

                rankSelect.innerHTML = '<option value="">اختر الرتبة</option>';
                ranks.forEach(rank => {
                    rankSelect.innerHTML += `<option value="${rank.id}">${rank.name}</option>`;
                });

                if (currentValue) rankSelect.value = currentValue;
            } catch (error) {
                console.error('خطأ في تحديث الرتب:', error);
            }
        }

        async function refreshAgencies() {
            try {
                const response = await fetch('/Officers/Create?handler=Agencies');
                if (!response.ok) throw new Error('Failed to fetch agencies');
                const agencies = await response.json();

                const agencySelect = document.getElementById('agencySelect');
                const currentValue = agencySelect.value;

                agencySelect.innerHTML = '<option value="">اختر الوكالة</option>';
                agencies.forEach(agency => {
                    agencySelect.innerHTML += `<option value="${agency.id}">${agency.name}</option>`;
                });

                if (currentValue) {
                    agencySelect.value = currentValue;
                    loadDirectorates();
                }
            } catch (error) {
                console.error('خطأ في تحديث الوكالات:', error);
            }
        }

        async function refreshDirectorates() {
            const agencyId = document.getElementById('agencySelect').value;
            if (agencyId) {
                await loadDirectorates();
            }
        }

        async function refreshDepartments() {
            const directorateId = document.getElementById('directorateSelect').value;
            if (directorateId) {
                await loadDepartments();
            }
        }

        async function refreshDivisions() {
            const departmentId = document.getElementById('departmentSelect').value;
            if (departmentId) {
                await loadDivisions();
            }
        }

        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }
    </script>
}
