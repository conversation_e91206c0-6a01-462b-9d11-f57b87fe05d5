@page
@model OfficersManagement.Pages.Divisions.EditModel
@{
    ViewData["Title"] = "تعديل الشعبة";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الشعبة
                </h2>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل بيانات الشعبة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        <input type="hidden" asp-for="Division.Id" />
                        <input type="hidden" asp-for="Division.CreatedAt" />
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Division.DepartmentId" class="form-label">القسم</label>
                                <select asp-for="Division.DepartmentId" asp-items="Model.DepartmentsList" class="form-select">
                                    <option value="">اختر القسم</option>
                                </select>
                                <span asp-validation-for="Division.DepartmentId" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Division.Name" class="form-label">اسم الشعبة</label>
                                <input asp-for="Division.Name" class="form-control" placeholder="أدخل اسم الشعبة" />
                                <span asp-validation-for="Division.Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-10 mb-3">
                                <label asp-for="Division.Description" class="form-label">الوصف</label>
                                <textarea asp-for="Division.Description" class="form-control" rows="3" placeholder="أدخل وصف الشعبة (اختياري)"></textarea>
                                <span asp-validation-for="Division.Description" class="text-danger"></span>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="form-check mt-4">
                                    <input asp-for="Division.IsActive" class="form-check-input" />
                                    <label asp-for="Division.IsActive" class="form-check-label">
                                        نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
