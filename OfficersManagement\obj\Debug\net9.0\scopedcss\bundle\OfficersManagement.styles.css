/* _content/OfficersManagement/Pages/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-l2aunv9ygu] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-l2aunv9ygu] {
  color: #0077cc;
}

.btn-primary[b-l2aunv9ygu] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-l2aunv9ygu], .nav-pills .show > .nav-link[b-l2aunv9ygu] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-l2aunv9ygu] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-l2aunv9ygu] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-l2aunv9ygu] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-l2aunv9ygu] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-l2aunv9ygu] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
