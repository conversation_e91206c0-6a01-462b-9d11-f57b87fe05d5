﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OfficersManagement.Migrations
{
    /// <inheritdoc />
    public partial class RemoveHealthStatusAndAddressFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Alley",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "District",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "HealthStatus",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "HouseNumber",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "Locality",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "NearestLandmark",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "Neighborhood",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "Subdistrict",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "Village",
                table: "Officers");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Alley",
                table: "Officers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "District",
                table: "Officers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HealthStatus",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "HouseNumber",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Locality",
                table: "Officers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NearestLandmark",
                table: "Officers",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Neighborhood",
                table: "Officers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Subdistrict",
                table: "Officers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Village",
                table: "Officers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "Alley", "District", "HealthStatus", "HouseNumber", "Locality", "NearestLandmark", "Neighborhood", "Subdistrict", "Village" },
                values: new object[] { null, null, "", null, null, null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "Alley", "District", "HealthStatus", "HouseNumber", "Locality", "NearestLandmark", "Neighborhood", "Subdistrict", "Village" },
                values: new object[] { null, null, "", null, null, null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "Alley", "District", "HealthStatus", "HouseNumber", "Locality", "NearestLandmark", "Neighborhood", "Subdistrict", "Village" },
                values: new object[] { null, null, "", null, null, null, null, null, null });
        }
    }
}
