@page
@model OfficersManagement.Pages.Departments.CreateModel
@{
    ViewData["Title"] = "إضافة قسم جديد";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-users-cog me-2"></i>
                    إضافة قسم جديد
                </h2>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        بيانات القسم الجديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Department.DirectorateId" class="form-label">المديرية</label>
                                <select asp-for="Department.DirectorateId" asp-items="Model.DirectoratesList" class="form-select">
                                    <option value="">اختر المديرية</option>
                                </select>
                                <span asp-validation-for="Department.DirectorateId" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Department.Name" class="form-label">اسم القسم</label>
                                <input asp-for="Department.Name" class="form-control" placeholder="أدخل اسم القسم" />
                                <span asp-validation-for="Department.Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-10 mb-3">
                                <label asp-for="Department.Description" class="form-label">الوصف</label>
                                <textarea asp-for="Department.Description" class="form-control" rows="3" placeholder="أدخل وصف القسم (اختياري)"></textarea>
                                <span asp-validation-for="Department.Description" class="text-danger"></span>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="form-check mt-4">
                                    <input asp-for="Department.IsActive" class="form-check-input" />
                                    <label asp-for="Department.IsActive" class="form-check-label">
                                        نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                حفظ القسم
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // إرسال رسالة للنافذة الأصلية عند النجاح
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من وجود رسالة نجاح
            const successMessage = '@TempData["SuccessMessage"]';
            if (successMessage && successMessage.trim() !== '') {
                // إرسال رسالة للنافذة الأصلية
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'DEPARTMENT_CREATED',
                        message: successMessage
                    }, '*');

                    // إغلاق النافذة بعد ثانيتين
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                }
            }
        });
    </script>
}
