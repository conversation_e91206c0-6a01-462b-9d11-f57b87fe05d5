using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Directorates
{
    public class DeleteModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public DeleteModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Directorate Directorate { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var directorate = await _context.Directorates
                .Include(d => d.Agency)
                .Include(d => d.Departments)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (directorate == null)
            {
                return NotFound();
            }
            else
            {
                Directorate = directorate;
            }
            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            try
            {
                var directorate = await _context.Directorates
                    .Include(d => d.Departments)
                    .FirstOrDefaultAsync(d => d.Id == id);

                if (directorate != null)
                {
                    // التحقق من وجود أقسام تابعة
                    if (directorate.Departments.Any())
                    {
                        TempData["ErrorMessage"] = "لا يمكن حذف المديرية لأنها تحتوي على أقسام تابعة. يرجى حذف الأقسام أولاً.";
                        return RedirectToPage("./Index");
                    }

                    _context.Directorates.Remove(directorate);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = $"تم حذف المديرية '{directorate.Name}' بنجاح";
                }

                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف المديرية: " + ex.Message;
                return RedirectToPage("./Index");
            }
        }
    }
}
