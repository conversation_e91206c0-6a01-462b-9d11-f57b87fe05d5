using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Districts
{
    public class IndexModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public IndexModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<District> Districts { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Districts = await _context.Districts
                .Include(d => d.Province)
                .Include(d => d.Subdistricts)
                .OrderBy(d => d.Province.Name)
                .ThenBy(d => d.Name)
                .ToListAsync();
        }
    }
}
