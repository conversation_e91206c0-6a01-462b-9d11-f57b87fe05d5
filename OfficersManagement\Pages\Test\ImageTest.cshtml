@page
@model OfficersManagement.Pages.Test.ImageTestModel
@{
    ViewData["Title"] = "اختبار رفع الصورة";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">اختبار وظيفة رفع الصورة</h4>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div id="photoPreview" style="width: 200px; height: 200px; border: 2px dashed #ddd; margin: 0 auto; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                            <span class="text-muted">لا توجد صورة</span>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <button type="button" class="btn btn-primary" onclick="selectFromFile()">
                            <i class="fas fa-folder-open me-1"></i>
                            اختيار صورة من ملف
                        </button>
                    </div>
                    
                    <input type="file" id="fileInput" class="d-none" accept="image/*" />
                    
                    <div class="mt-4">
                        <h5>معلومات التشخيص:</h5>
                        <div id="diagnostics" class="alert alert-info">
                            <p>جاري التحميل...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة الاختبار');
    
    const fileInput = document.getElementById('fileInput');
    const photoPreview = document.getElementById('photoPreview');
    const diagnostics = document.getElementById('diagnostics');
    
    // تحديث معلومات التشخيص
    diagnostics.innerHTML = `
        <p><strong>حالة العناصر:</strong></p>
        <ul>
            <li>fileInput: ${fileInput ? 'موجود' : 'غير موجود'}</li>
            <li>photoPreview: ${photoPreview ? 'موجود' : 'غير موجود'}</li>
            <li>diagnostics: ${diagnostics ? 'موجود' : 'غير موجود'}</li>
        </ul>
        <p><strong>المتصفح:</strong> ${navigator.userAgent}</p>
    `;
    
    // إضافة مستمع لتغيير الملف
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            console.log('تم تغيير الملف في صفحة الاختبار');
            const file = e.target.files[0];
            if (file) {
                console.log('تم اختيار ملف:', file.name, 'حجم:', file.size);
                handleImageSelection(file);
            }
        });
        console.log('تم إضافة مستمع تغيير الملف في صفحة الاختبار');
    }
});

function selectFromFile() {
    console.log('تم النقر على زر اختيار الملف في صفحة الاختبار');
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        console.log('سيتم فتح نافذة اختيار الملف');
        fileInput.value = '';
        fileInput.click();
    } else {
        console.error('لم يتم العثور على عنصر fileInput');
        alert('خطأ: لم يتم العثور على عنصر اختيار الملف');
    }
}

function handleImageSelection(file) {
    console.log('بدء معالجة الصورة:', file.name);
    
    if (!file.type.startsWith('image/')) {
        alert('يرجى اختيار ملف صورة صحيح');
        return;
    }
    
    if (file.size > 5 * 1024 * 1024) {
        alert('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        console.log('تم قراءة الملف بنجاح');
        const photoPreview = document.getElementById('photoPreview');
        if (photoPreview) {
            photoPreview.innerHTML = `<img src="${e.target.result}" alt="معاينة الصورة" style="width: 100%; height: 100%; object-fit: cover; border-radius: 6px;" />`;
            console.log('تم تحديث معاينة الصورة');
        }
    };
    
    reader.onerror = function(err) {
        console.error('خطأ في قراءة الملف:', err);
        alert('حدث خطأ في قراءة الملف');
    };
    
    reader.readAsDataURL(file);
}
</script>
