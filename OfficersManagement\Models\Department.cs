using System.ComponentModel.DataAnnotations;

namespace OfficersManagement.Models
{
    public class Department
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم القسم مطلوب")]
        [Display(Name = "اسم القسم")]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        [StringLength(500)]
        public string? Description { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Required]
        [Display(Name = "المديرية")]
        public int DirectorateId { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Directorate? Directorate { get; set; }
        public virtual ICollection<Division> Divisions { get; set; } = new List<Division>();
    }
}
