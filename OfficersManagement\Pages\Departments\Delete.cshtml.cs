using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Departments
{
    public class DeleteModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public DeleteModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Department Department { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var department = await _context.Departments
                .Include(d => d.Directorate)
                    .ThenInclude(dir => dir!.Agency)
                .Include(d => d.Divisions)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (department == null)
            {
                return NotFound();
            }
            else
            {
                Department = department;
            }
            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            try
            {
                var department = await _context.Departments
                    .Include(d => d.Divisions)
                    .FirstOrDefaultAsync(d => d.Id == id);

                if (department != null)
                {
                    // التحقق من وجود شعب تابعة
                    if (department.Divisions.Any())
                    {
                        TempData["ErrorMessage"] = "لا يمكن حذف القسم لأنه يحتوي على شعب تابعة. يرجى حذف الشعب أولاً.";
                        return RedirectToPage("./Index");
                    }

                    _context.Departments.Remove(department);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = $"تم حذف القسم '{department.Name}' بنجاح";
                }

                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف القسم: " + ex.Message;
                return RedirectToPage("./Index");
            }
        }
    }
}
