@page
@model OfficersManagement.Pages.Departments.EditModel
@{
    ViewData["Title"] = "تعديل القسم";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-edit me-2"></i>
                    تعديل القسم
                </h2>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل بيانات القسم
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        <input type="hidden" asp-for="Department.Id" />
                        <input type="hidden" asp-for="Department.CreatedAt" />
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Department.DirectorateId" class="form-label">المديرية</label>
                                <select asp-for="Department.DirectorateId" asp-items="Model.DirectoratesList" class="form-select">
                                    <option value="">اختر المديرية</option>
                                </select>
                                <span asp-validation-for="Department.DirectorateId" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Department.Name" class="form-label">اسم القسم</label>
                                <input asp-for="Department.Name" class="form-control" placeholder="أدخل اسم القسم" />
                                <span asp-validation-for="Department.Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-10 mb-3">
                                <label asp-for="Department.Description" class="form-label">الوصف</label>
                                <textarea asp-for="Department.Description" class="form-control" rows="3" placeholder="أدخل وصف القسم (اختياري)"></textarea>
                                <span asp-validation-for="Department.Description" class="text-danger"></span>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="form-check mt-4">
                                    <input asp-for="Department.IsActive" class="form-check-input" />
                                    <label asp-for="Department.IsActive" class="form-check-label">
                                        نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
