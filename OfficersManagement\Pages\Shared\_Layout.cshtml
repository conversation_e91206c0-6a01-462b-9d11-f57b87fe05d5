﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة الضباط</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/OfficersManagement.styles.css" asp-append-version="true" />
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .jumbotron {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        .card {
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', sans-serif;
        }
        .btn {
            font-family: 'Cairo', sans-serif;
        }
        .form-label {
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
        }

        /* ضمان stretch للصور في جميع أنحاء التطبيق */
        .photo-container img,
        .photo-preview-container img,
        #photoPreview img {
            object-fit: fill !important;
            display: block !important;
        }

        /* تحسين عرض الصور المصغرة */
        .table img {
            object-fit: fill !important;
            display: block !important;
        }

        /* ضمان stretch للكاميرا */
        #cameraVideo,
        #cameraCanvas {
            object-fit: fill !important;
        }

        /* جعل مودال الكاميرا أعلى كل شيء */
        #cameraModal {
            z-index: 9999 !important;
        }

        #cameraModal .modal-backdrop {
            z-index: 9998 !important;
        }

        /* تحسين مظهر مودال الكاميرا */
        #cameraModal .modal-dialog {
            margin-top: 2rem;
        }

        #cameraModal .modal-content {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow">
            <div class="container">
                <a class="navbar-brand" asp-area="" asp-page="/Index">
                    <i class="fas fa-shield-alt me-2"></i>
                    نظام إدارة الضباط
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-page="/Index">
                                <i class="fas fa-home me-1"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownOfficers" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-users me-1"></i>
                                إدارة الضباط
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-page="/Officers/Index">
                                    <i class="fas fa-list me-1"></i>
                                    قائمة الضباط
                                </a></li>
                                <li><a class="dropdown-item" asp-page="/Officers/Create">
                                    <i class="fas fa-user-plus me-1"></i>
                                    إضافة ضابط جديد
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownConstants" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-cogs me-1"></i>
                                الثوابت
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-page="/Ranks/Index">
                                    <i class="fas fa-star me-1"></i>
                                    إدارة الرتب
                                </a></li>
                                <li><a class="dropdown-item" asp-page="/Ranks/Create">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة رتبة جديدة
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" asp-page="/Provinces/Index">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    إدارة المحافظات
                                </a></li>
                                <li><a class="dropdown-item" asp-page="/Provinces/Create">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة محافظة جديدة
                                </a></li>

                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" asp-page="/Test/PhotoInfo">
                                    <i class="fas fa-database me-1"></i>
                                    معلومات الصور
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <span class="navbar-text">
                                <i class="fas fa-calendar me-1"></i>
                                @DateTime.Now.ToString("yyyy/MM/dd")
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <!-- Success/Error Messages -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="container mt-3">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                @TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="container mt-3">
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                @TempData["ErrorMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    }

    <div class="container-fluid">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>نظام إدارة الضباط والمنتسبين</h6>
                    <p class="mb-0">نظام شامل لإدارة بيانات الضباط في القوات المسلحة</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        &copy; 2025 - جميع الحقوق محفوظة
                    </p>
                    <small class="text-muted">
                        تم التطوير باستخدام ASP.NET Core
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
