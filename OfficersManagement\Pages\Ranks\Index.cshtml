@page
@model OfficersManagement.Pages.Ranks.IndexModel
@{
    ViewData["Title"] = "إدارة الرتب";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-star me-2"></i>
                            إدارة الرتب العسكرية
                        </h4>
                        <a asp-page="./Create" class="btn btn-light">
                            <i class="fas fa-plus me-1"></i>
                            إضافة رتبة جديدة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- شريط البحث -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="get">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="searchString" 
                                           value="@Model.CurrentFilter" placeholder="البحث بالاسم أو الوصف...">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    @if (!string.IsNullOrEmpty(Model.CurrentFilter))
                                    {
                                        <a asp-page="./Index" class="btn btn-outline-danger">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    }
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge bg-info fs-6">
                                إجمالي الرتب: @Model.Ranks.Count()
                            </span>
                        </div>
                    </div>

                    <!-- جدول الرتب -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>
                                        <a asp-page="./Index" asp-route-sortOrder="@Model.OrderSort" 
                                           asp-route-currentFilter="@Model.CurrentFilter" class="text-white text-decoration-none">
                                            الترتيب
                                            @if (Model.CurrentSort == "order")
                                            {
                                                <i class="fas fa-sort-up"></i>
                                            }
                                            else if (Model.CurrentSort == "order_desc")
                                            {
                                                <i class="fas fa-sort-down"></i>
                                            }
                                            else
                                            {
                                                <i class="fas fa-sort"></i>
                                            }
                                        </a>
                                    </th>
                                    <th>
                                        <a asp-page="./Index" asp-route-sortOrder="@Model.NameSort" 
                                           asp-route-currentFilter="@Model.CurrentFilter" class="text-white text-decoration-none">
                                            اسم الرتبة
                                            @if (Model.CurrentSort == "name")
                                            {
                                                <i class="fas fa-sort-up"></i>
                                            }
                                            else if (Model.CurrentSort == "name_desc")
                                            {
                                                <i class="fas fa-sort-down"></i>
                                            }
                                            else
                                            {
                                                <i class="fas fa-sort"></i>
                                            }
                                        </a>
                                    </th>
                                    <th>الوصف</th>
                                    <th>عدد الضباط</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var rank in Model.Ranks)
                                {
                                    <tr class="@(rank.IsActive ? "" : "table-secondary")">
                                        <td>
                                            <span class="badge bg-primary">@rank.Order</span>
                                        </td>
                                        <td>
                                            <strong>@rank.Name</strong>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(rank.Description))
                                            {
                                                <small class="text-muted">@rank.Description</small>
                                            }
                                            else
                                            {
                                                <small class="text-muted">لا يوجد وصف</small>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@rank.Officers.Count</span>
                                        </td>
                                        <td>
                                            @if (rank.IsActive)
                                            {
                                                <span class="badge bg-success">نشط</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">غير نشط</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-page="./Details" asp-route-id="@rank.Id" 
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@rank.Id" 
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if (rank.Officers.Count == 0)
                                                {
                                                    <a asp-page="./Delete" asp-route-id="@rank.Id" 
                                                       class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                }
                                                else
                                                {
                                                    <button class="btn btn-sm btn-outline-secondary" disabled title="لا يمكن حذف رتبة مرتبطة بضباط">
                                                        <i class="fas fa-lock"></i>
                                                    </button>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Ranks.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-star fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد رتب</h5>
                            <p class="text-muted">قم بإضافة رتبة جديدة للبدء</p>
                            <a asp-page="./Create" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>
                                إضافة رتبة جديدة
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // تأكيد الحذف
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('a[href*="/Delete"]');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (!confirm('هل أنت متأكد من حذف هذه الرتبة؟')) {
                        e.preventDefault();
                    }
                });
            });
        });
    </script>
}
