using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Directorates
{
    public class IndexModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public IndexModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<Directorate> Directorates { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Directorates = await _context.Directorates
                .Include(d => d.Agency)
                .Include(d => d.Departments)
                .OrderBy(d => d.Agency!.Name)
                .ThenBy(d => d.Name)
                .ToListAsync();
        }
    }
}
