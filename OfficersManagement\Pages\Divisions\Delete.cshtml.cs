using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Pages.Divisions
{
    public class DeleteModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public DeleteModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Division Division { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var division = await _context.Divisions
                .Include(d => d.Department)
                    .ThenInclude(dept => dept!.Directorate)
                        .ThenInclude(dir => dir!.Agency)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (division == null)
            {
                return NotFound();
            }
            else
            {
                Division = division;
            }
            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            try
            {
                var division = await _context.Divisions.FirstOrDefaultAsync(d => d.Id == id);

                if (division != null)
                {
                    _context.Divisions.Remove(division);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = $"تم حذف الشعبة '{division.Name}' بنجاح";
                }

                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف الشعبة: " + ex.Message;
                return RedirectToPage("./Index");
            }
        }
    }
}
