@page
@model OfficersManagement.Pages.Provinces.CreateModel
@{
    ViewData["Title"] = "إضافة محافظة جديدة";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        إضافة محافظة جديدة
                    </h4>
                </div>

                <div class="card-body">
                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label asp-for="Province.Name" class="form-label"></label>
                                <input asp-for="Province.Name" class="form-control" placeholder="أدخل اسم المحافظة" />
                                <span asp-validation-for="Province.Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label asp-for="Province.Description" class="form-label"></label>
                                <textarea asp-for="Province.Description" class="form-control" rows="3" placeholder="أدخل وصف المحافظة (اختياري)"></textarea>
                                <span asp-validation-for="Province.Description" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-page="./Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للقائمة
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ المحافظة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
