﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OfficersManagement.Migrations
{
    /// <inheritdoc />
    public partial class AddSeparateNameFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Officers_FullName",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "FullName",
                table: "Officers");

            migrationBuilder.AddColumn<string>(
                name: "FirstName",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "FourthName",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "SecondName",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ThirdName",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "FirstName", "FourthName", "SecondName", "ThirdName", "Title" },
                values: new object[] { "أحمد", "الأحمد", "محمد", "علي", "الدكتور" });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "FirstName", "FourthName", "SecondName", "ThirdName", "Title" },
                values: new object[] { "فاطمة", "الزهراء", "حسن", "عبدالله", "المهندسة" });

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "FirstName", "FourthName", "SecondName", "ThirdName", "Title" },
                values: new object[] { "خالد", "المحمود", "عبدالرحمن", "صالح", "الأستاذ" });

            migrationBuilder.CreateIndex(
                name: "IX_Officers_FullName",
                table: "Officers",
                columns: new[] { "FirstName", "SecondName", "ThirdName", "FourthName" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Officers_Name",
                table: "Officers",
                columns: new[] { "FirstName", "FourthName" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Officers_FullName",
                table: "Officers");

            migrationBuilder.DropIndex(
                name: "IX_Officers_Name",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "FirstName",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "FourthName",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "SecondName",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "ThirdName",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "Officers");

            migrationBuilder.AddColumn<string>(
                name: "FullName",
                table: "Officers",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 1,
                column: "FullName",
                value: "الدكتور أحمد محمد علي الأحمد");

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 2,
                column: "FullName",
                value: "المهندسة فاطمة حسن عبدالله الزهراء");

            migrationBuilder.UpdateData(
                table: "Officers",
                keyColumn: "Id",
                keyValue: 3,
                column: "FullName",
                value: "الأستاذ خالد عبدالرحمن صالح المحمود");

            migrationBuilder.CreateIndex(
                name: "IX_Officers_FullName",
                table: "Officers",
                column: "FullName",
                unique: true);
        }
    }
}
